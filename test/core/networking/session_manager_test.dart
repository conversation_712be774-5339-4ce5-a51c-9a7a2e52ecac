import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:nextsportz_v2/core/networking/session_manager.dart';
import 'package:nextsportz_v2/core/local/token_storage.dart';

import 'session_manager_test.mocks.dart';

@GenerateMocks([TokenStorageService])
void main() {
  group('SessionManager', () {
    late MockTokenStorageService mockTokenStorage;
    late SessionManager sessionManager;

    setUp(() {
      mockTokenStorage = MockTokenStorageService();
      sessionManager = SessionManager(mockTokenStorage);
    });

    tearDown(() {
      sessionManager.dispose();
    });

    group('clearSession', () {
      test('should clear tokens and emit session cleared event', () async {
        // Arrange
        when(mockTokenStorage.clearTokens()).thenAnswer((_) async {});

        final events = <SessionEvent>[];
        sessionManager.sessionEvents.listen(events.add);

        // Act
        await sessionManager.clearSession(
          reason: SessionClearReason.userLogout,
        );

        // Assert
        verify(mockTokenStorage.clearTokens()).called(1);
        expect(events, hasLength(1));
        expect(events.first.type, SessionEventType.sessionCleared);
        expect(events.first.reason, SessionClearReason.userLogout);
      });

      test(
        'should emit session clear failed event when clearTokens throws',
        () async {
          // Arrange
          when(
            mockTokenStorage.clearTokens(),
          ).thenThrow(Exception('Storage error'));

          final events = <SessionEvent>[];
          sessionManager.sessionEvents.listen(events.add);

          // Act
          await sessionManager.clearSession(
            reason: SessionClearReason.tokenRefreshFailed,
          );

          // Assert
          verify(mockTokenStorage.clearTokens()).called(1);
          expect(events, hasLength(1));
          expect(events.first.type, SessionEventType.sessionClearFailed);
          expect(events.first.reason, SessionClearReason.tokenRefreshFailed);
          expect(events.first.error, contains('Storage error'));
        },
      );
    });

    group('isAuthenticated', () {
      test('should return true when tokens exist', () async {
        // Arrange
        when(mockTokenStorage.getTokens()).thenAnswer(
          (_) async => {
            'accessToken': 'test_token',
            'refreshToken': 'refresh_token',
          },
        );

        // Act
        final result = await sessionManager.isAuthenticated();

        // Assert
        expect(result, isTrue);
        verify(mockTokenStorage.getTokens()).called(1);
      });

      test('should return false when no tokens exist', () async {
        // Arrange
        when(mockTokenStorage.getTokens()).thenAnswer((_) async => null);

        // Act
        final result = await sessionManager.isAuthenticated();

        // Assert
        expect(result, isFalse);
        verify(mockTokenStorage.getTokens()).called(1);
      });

      test('should return false when access token is null', () async {
        // Arrange
        when(mockTokenStorage.getTokens()).thenAnswer(
          (_) async => {'accessToken': null, 'refreshToken': 'refresh_token'},
        );

        // Act
        final result = await sessionManager.isAuthenticated();

        // Assert
        expect(result, isFalse);
      });

      test('should return false when getTokens throws', () async {
        // Arrange
        when(
          mockTokenStorage.getTokens(),
        ).thenThrow(Exception('Storage error'));

        // Act
        final result = await sessionManager.isAuthenticated();

        // Assert
        expect(result, isFalse);
      });
    });

    group('notification methods', () {
      test('notifyTokenRefreshFailed should emit correct event', () {
        // Arrange
        final events = <SessionEvent>[];
        sessionManager.sessionEvents.listen(events.add);

        // Act
        sessionManager.notifyTokenRefreshFailed();

        // Assert
        expect(events, hasLength(1));
        expect(events.first.type, SessionEventType.tokenRefreshFailed);
        expect(events.first.reason, SessionClearReason.tokenRefreshFailed);
      });

      test('notifySessionExpired should emit correct event', () {
        // Arrange
        final events = <SessionEvent>[];
        sessionManager.sessionEvents.listen(events.add);

        // Act
        sessionManager.notifySessionExpired();

        // Assert
        expect(events, hasLength(1));
        expect(events.first.type, SessionEventType.sessionExpired);
        expect(events.first.reason, SessionClearReason.sessionExpired);
      });
    });

    group('session events stream', () {
      test('should emit multiple events correctly', () async {
        // Arrange
        final events = <SessionEvent>[];
        sessionManager.sessionEvents.listen(events.add);
        when(mockTokenStorage.clearTokens()).thenAnswer((_) async {});

        // Act
        sessionManager.notifyTokenRefreshFailed();
        await sessionManager.clearSession(
          reason: SessionClearReason.userLogout,
        );
        sessionManager.notifySessionExpired();

        // Assert
        expect(events, hasLength(3));
        expect(events[0].type, SessionEventType.tokenRefreshFailed);
        expect(events[1].type, SessionEventType.sessionCleared);
        expect(events[2].type, SessionEventType.sessionExpired);
      });

      test('should allow multiple listeners', () {
        // Arrange
        final events1 = <SessionEvent>[];
        final events2 = <SessionEvent>[];
        sessionManager.sessionEvents.listen(events1.add);
        sessionManager.sessionEvents.listen(events2.add);

        // Act
        sessionManager.notifyTokenRefreshFailed();

        // Assert
        expect(events1, hasLength(1));
        expect(events2, hasLength(1));
        expect(events1.first.type, SessionEventType.tokenRefreshFailed);
        expect(events2.first.type, SessionEventType.tokenRefreshFailed);
      });
    });

    group('SessionEvent', () {
      test('should create event with all properties', () {
        // Arrange & Act
        final event = SessionEvent(
          type: SessionEventType.sessionCleared,
          reason: SessionClearReason.userLogout,
          timestamp: DateTime(2023, 1, 1),
          error: 'Test error',
        );

        // Assert
        expect(event.type, SessionEventType.sessionCleared);
        expect(event.reason, SessionClearReason.userLogout);
        expect(event.timestamp, DateTime(2023, 1, 1));
        expect(event.error, 'Test error');
      });

      test('toString should include all properties', () {
        // Arrange
        final timestamp = DateTime(2023, 1, 1);
        final event = SessionEvent(
          type: SessionEventType.sessionCleared,
          reason: SessionClearReason.userLogout,
          timestamp: timestamp,
          error: 'Test error',
        );

        // Act
        final string = event.toString();

        // Assert
        expect(string, contains('SessionEventType.sessionCleared'));
        expect(string, contains('SessionClearReason.userLogout'));
        expect(string, contains(timestamp.toString()));
        expect(string, contains('Test error'));
      });
    });
  });
}
