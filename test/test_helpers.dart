import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Test helper class with common utilities for testing
class TestHelpers {
  /// Creates a test widget with ProviderScope
  static Widget createTestWidget({
    required Widget child,
    List<Override> overrides = const [],
  }) {
    return ProviderScope(overrides: overrides, child: MaterialApp(home: child));
  }

  /// Creates a test widget with Material<PERSON>pp and ProviderScope
  static Widget createTestApp({
    required Widget child,
    List<Override> overrides = const [],
    ThemeData? theme,
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(theme: theme ?? ThemeData(), home: child),
    );
  }

  /// Waits for async operations to complete
  static Future<void> waitForAsync(WidgetTester tester) async {
    await tester.pump();
    await tester.pumpAndSettle();
  }

  /// Finds a widget by type and index
  static Finder findWidgetByType<T>(int index) {
    return find.byType(T).at(index);
  }

  /// Finds text that contains the given string
  static Finder findTextContaining(String text) {
    return find.textContaining(text);
  }

  /// Finds a widget by key
  static Finder findWidgetByKey(Key key) {
    return find.byKey(key);
  }

  /// Finds a widget by icon
  static Finder findWidgetByIcon(IconData icon) {
    return find.byIcon(icon);
  }

  /// Taps on a widget and waits for animations
  static Future<void> tapAndWait(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await waitForAsync(tester);
  }

  /// Enters text in a text field and waits
  static Future<void> enterTextAndWait(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await waitForAsync(tester);
  }

  /// Scrolls to find a widget
  static Future<void> scrollToFind(WidgetTester tester, Finder finder) async {
    await tester.scrollUntilVisible(finder, 500.0);
    await waitForAsync(tester);
  }

  /// Checks if a widget is visible
  static bool isWidgetVisible(Finder finder) {
    return finder.evaluate().isNotEmpty;
  }

  /// Gets the text from a text widget
  static String getTextFromWidget(Finder finder) {
    final widget = finder.evaluate().single.widget;
    if (widget is Text) {
      return widget.data ?? '';
    }
    return '';
  }

  /// Creates a mock provider override
  static Override createMockProvider<T>(Provider<T> provider, T mockValue) {
    return provider.overrideWithValue(mockValue);
  }

  /// Creates multiple mock provider overrides
  static List<Override> createMockProviders(List<Override> overrides) {
    return overrides;
  }

  /// Waits for a specific condition to be true
  static Future<void> waitForCondition(
    WidgetTester tester,
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final startTime = DateTime.now();
    while (!condition()) {
      if (DateTime.now().difference(startTime) > timeout) {
        throw TimeoutException('Condition not met within timeout period');
      }
      await tester.pump(const Duration(milliseconds: 100));
    }
  }

  /// Finds a widget by its semantic label
  static Finder findWidgetBySemanticsLabel(String label) {
    return find.bySemanticsLabel(label);
  }

  /// Finds a widget by its tooltip
  static Finder findWidgetByTooltip(String tooltip) {
    return find.byTooltip(tooltip);
  }

  /// Checks if a widget has a specific property
  static bool hasProperty<T extends Widget>(
    Finder finder,
    bool Function(T widget) property,
  ) {
    final widgets = finder.evaluate();
    return widgets.any((element) {
      final widget = element.widget;
      if (widget is T) {
        return property(widget);
      }
      return false;
    });
  }

  /// Gets the count of widgets found by a finder
  static int getWidgetCount(Finder finder) {
    return finder.evaluate().length;
  }

  /// Asserts that a widget is found exactly once
  static void expectWidgetFoundOnce(Finder finder) {
    expect(finder, findsOneWidget);
  }

  /// Asserts that a widget is found exactly n times
  static void expectWidgetFoundNTimes(Finder finder, int n) {
    expect(finder, findsNWidgets(n));
  }

  /// Asserts that a widget is not found
  static void expectWidgetNotFound(Finder finder) {
    expect(finder, findsNothing);
  }

  /// Asserts that a widget is found at least once
  static void expectWidgetFound(Finder finder) {
    expect(finder, findsAtLeastOneWidget);
  }

  /// Asserts that a widget is found at least n times
  static void expectWidgetFoundAtLeast(Finder finder, int n) {
    expect(finder, findsAtLeastNWidgets(n));
  }
}

/// Custom exception for test timeouts
class TimeoutException implements Exception {
  final String message;
  TimeoutException(this.message);

  @override
  String toString() => 'TimeoutException: $message';
}

/// Test data factory for creating test objects
class TestDataFactory {
  /// Creates a test user
  static Map<String, dynamic> createTestUser({
    String id = '1',
    String name = 'Test User',
    String email = '<EMAIL>',
    String phoneNumber = '1234567890',
    String role = 'PLAYER',
  }) {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'role': role,
    };
  }

  /// Creates test login credentials
  static Map<String, String> createTestCredentials({
    String phoneNumber = '1234567890',
    String password = 'password123',
    String role = 'PLAYER',
  }) {
    return {'phoneNumber': phoneNumber, 'password': password, 'role': role};
  }

  /// Creates test form data
  static Map<String, String> createTestFormData({
    String fullName = 'Test User',
    String email = '<EMAIL>',
    String phoneNumber = '1234567890',
    String password = 'password123',
    String role = 'PLAYER',
  }) {
    return {
      'fullName': fullName,
      'email': email,
      'phoneNumber': phoneNumber,
      'password': password,
      'role': role,
    };
  }
}

/// Test matchers for common assertions
class TestMatchers {
  /// Matches a widget that has a specific text
  static Matcher hasText(String text) {
    return predicate((widget) {
      if (widget is Text) {
        return widget.data == text;
      }
      return false;
    });
  }

  /// Matches a widget that contains specific text
  static Matcher containsText(String text) {
    return predicate((widget) {
      if (widget is Text) {
        return widget.data?.contains(text) ?? false;
      }
      return false;
    });
  }

  /// Matches a widget that has a specific color
  static Matcher hasColor(Color color) {
    return predicate((widget) {
      if (widget is Container) {
        return widget.color == color;
      }
      return false;
    });
  }

  /// Matches a widget that is enabled
  static Matcher isEnabled() {
    return predicate((widget) {
      if (widget is TextFormField) {
        return widget.enabled;
      }
      return false;
    });
  }

  /// Matches a widget that is disabled
  static Matcher isDisabled() {
    return predicate((widget) {
      if (widget is TextFormField) {
        return !widget.enabled;
      }
      return false;
    });
  }
}
