import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/features/auth/data/datasources/auth_remote_datasource.dart';
import 'package:nextsportz_v2/features/auth/data/dto/auth_request_models.dart';
import 'package:nextsportz_v2/features/auth/data/dto/auth_response_models.dart';
import 'package:dio/dio.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';

import 'auth_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  group('AuthRemoteDatasource', () {
    late MockApiClient mockApiClient;
    late AuthRemoteDataSource authRemoteDatasource;

    setUp(() {
      mockApiClient = MockApiClient();
      authRemoteDatasource = AuthRemoteDataSource(mockApiClient);
    });

    group('register', () {
      test('should register user successfully', () async {
        // Arrange
        final request = RegisterRequest(
          fullName: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          password: 'password123',
          role: 'PLAYER',
        );

        final response = SuccessResponse(
          success: true,
          message: 'User registered successfully',
        );

        when(
          mockApiClient.post('/auth/register', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.register(request);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('User registered successfully'));
        verify(
          mockApiClient.post('/auth/register', data: request.toJson()),
        ).called(1);
      });

      test('should handle registration failure', () async {
        // Arrange
        final request = RegisterRequest(
          fullName: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          password: 'password123',
          role: 'PLAYER',
        );

        final response = SuccessResponse(
          success: false,
          message: 'Registration failed',
        );

        when(
          mockApiClient.post('/auth/register', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.register(request);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, equals('Registration failed'));
      });
    });

    test('should bubble DioExceptionHandle on transport error (register)',
        () async {
      final request = RegisterRequest(
        fullName: 'Test User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        password: 'password123',
        role: 'PLAYER',
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/auth/register'),
        response: Response(
            requestOptions: RequestOptions(path: '/auth/register'),
            statusCode: 500),
        type: DioExceptionType.badResponse,
        message: 'Server error',
      );

      when(
        mockApiClient.post('/auth/register', data: request.toJson()),
      ).thenThrow(DioExceptionHandle.fromDioError(dioError));

      expect(
        () => authRemoteDatasource.register(request),
        throwsA(isA<DioExceptionHandle>()),
      );
    });

    test('should bubble DioExceptionHandle on transport error (verifyOtp)',
        () async {
      final request = RegisterVerifyRequest(
        phoneNumber: '1234567890',
        otp: '0000',
      );

      final dioError = DioException(
        requestOptions: RequestOptions(path: '/auth/verify-otp'),
        response: Response(
            requestOptions: RequestOptions(path: '/auth/verify-otp'),
            statusCode: 400),
        type: DioExceptionType.badResponse,
        message: 'Bad request',
      );

      when(
        mockApiClient.post('/auth/verify-otp', data: request.toJson()),
      ).thenThrow(DioExceptionHandle.fromDioError(dioError));

      expect(
        () => authRemoteDatasource.verifyOtp(request),
        throwsA(isA<DioExceptionHandle>()),
      );
    });

    group('verifyOtp', () {
      test('should verify OTP successfully', () async {
        // Arrange
        final request = RegisterVerifyRequest(
          phoneNumber: '1234567890',
          otp: '123456',
        );

        final response = SuccessResponse(
          success: true,
          message: 'OTP verified successfully',
        );

        when(
          mockApiClient.post('/auth/verify-otp', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.verifyOtp(request);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('OTP verified successfully'));
        verify(
          mockApiClient.post('/auth/verify-otp', data: request.toJson()),
        ).called(1);
      });
    });

    group('login', () {
      test('should login successfully', () async {
        // Arrange
        final request = LoginRequest(
          phoneNumber: '1234567890',
          password: 'password123',
          role: 'PLAYER',
        );

        final loginResponse = LoginResponse(
          tokens: AuthTokenResponse(
            accessToken: 'access_token',
            refreshToken: 'refresh_token',
          ),
          user: UserProfileResponse(
            userId: '1',
            fullName: 'Test User',
            email: '<EMAIL>',
            phoneNumber: '1234567890',
            role: 'PLAYER',
          ),
        );

        when(
          mockApiClient.post('/auth/login', data: request.toJson()),
        ).thenAnswer((_) async => loginResponse.toJson());

        // Act
        final result = await authRemoteDatasource.login(request);

        // Assert
        expect(result.tokens?.accessToken, equals('access_token'));
        expect(result.tokens?.refreshToken, equals('refresh_token'));
        expect(result.user?.fullName, equals('Test User'));
        verify(
          mockApiClient.post('/auth/login', data: request.toJson()),
        ).called(1);
      });

      test('should bubble DioExceptionHandle on transport error (login)',
          () async {
        final request = LoginRequest(
          phoneNumber: '1234567890',
          password: 'wrong_password',
          role: 'PLAYER',
        );

        final dioError = DioException(
          requestOptions: RequestOptions(path: '/auth/login'),
          response: Response(
              requestOptions: RequestOptions(path: '/auth/login'),
              statusCode: 401,
              data: {'error': 'Unauthorized'}),
          type: DioExceptionType.badResponse,
          message: 'Unauthorized',
        );

        when(
          mockApiClient.post('/auth/login', data: request.toJson()),
        ).thenThrow(DioExceptionHandle.fromDioError(dioError));

        expect(
          () => authRemoteDatasource.login(request),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('getCurrentUser', () {
      test('should get current user successfully', () async {
        // Arrange
        final userProfile = UserProfileResponse(
          userId: '1',
          fullName: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          role: 'PLAYER',
        );

        when(
          mockApiClient.get('/auth/me'),
        ).thenAnswer((_) async => {
              'id': '1',
              'fullName': 'Test User',
              'email': '<EMAIL>',
              'phoneNumber': '1234567890',
              'role': 'PLAYER',
            });

        // Act
        final result = await authRemoteDatasource.getCurrentUser();

        // Assert
        expect(result.userId, equals('1'));
        expect(result.fullName, equals('Test User'));
        expect(result.email, equals('<EMAIL>'));
        verify(mockApiClient.get('/auth/me')).called(1);
      });
    });

    group('refreshToken', () {
      test('should refresh token successfully', () async {
        // Arrange
        const refreshToken = 'old_refresh_token';
        final tokens = AuthTokenResponse(
          accessToken: 'new_access_token',
          refreshToken: 'new_refresh_token',
        );

        when(
          mockApiClient.post(
            '/auth/refresh',
            data: {'refreshToken': refreshToken},
          ),
        ).thenAnswer((_) async => tokens.toJson());

        // Act
        final result = await authRemoteDatasource.refreshToken(refreshToken);

        // Assert
        expect(result.accessToken, equals('new_access_token'));
        expect(result.refreshToken, equals('new_refresh_token'));
        verify(
          mockApiClient.post(
            '/auth/refresh',
            data: {'refreshToken': refreshToken},
          ),
        ).called(1);
      });
    });

    group('logout', () {
      test('should logout successfully', () async {
        // Arrange
        const refreshToken = 'refresh_token';
        final response = SuccessResponse(
          success: true,
          message: 'Logged out successfully',
        );

        when(
          mockApiClient.post(
            '/auth/logout',
            data: {'refreshToken': refreshToken},
          ),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.logout(
          refreshToken: refreshToken,
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('Logged out successfully'));
        verify(
          mockApiClient.post(
            '/auth/logout',
            data: {'refreshToken': refreshToken},
          ),
        ).called(1);
      });

      test('should logout without refresh token', () async {
        // Arrange
        final response = SuccessResponse(
          success: true,
          message: 'Logged out successfully',
        );

        when(
          mockApiClient.post('/auth/logout', data: null),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.logout();

        // Assert
        expect(result.success, isTrue);
        verify(mockApiClient.post('/auth/logout', data: null)).called(1);
      });
    });

    group('forgotPassword', () {
      test('should send forgot password email successfully', () async {
        // Arrange
        final request = ForgotPasswordRequest(email: '<EMAIL>');

        final response = SuccessResponse(
          success: true,
          message: 'Password reset email sent',
        );

        when(
          mockApiClient.post('/auth/forgot-password', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.forgotPassword(request);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('Password reset email sent'));
        verify(
          mockApiClient.post('/auth/forgot-password', data: request.toJson()),
        ).called(1);
      });
    });

    group('resetPassword', () {
      test('should reset password successfully', () async {
        // Arrange
        final request = ResetPasswordRequest(
          token: 'reset_token',
          newPassword: 'new_password123',
        );

        final response = SuccessResponse(
          success: true,
          message: 'Password reset successfully',
        );

        when(
          mockApiClient.post('/auth/reset-password', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.resetPassword(request);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('Password reset successfully'));
        verify(
          mockApiClient.post('/auth/reset-password', data: request.toJson()),
        ).called(1);
      });
    });

    group('updateProfile', () {
      test('should update profile successfully', () async {
        // Arrange
        final request = UpdateProfileRequest(
          name: 'Updated Name',
          email: '<EMAIL>',
        );

        final response = SuccessResponse(
          success: true,
          message: 'Profile updated successfully',
        );

        when(
          mockApiClient.put('/auth/update-profile', data: request.toJson()),
        ).thenAnswer((_) async => response.toJson());

        // Act
        final result = await authRemoteDatasource.updateProfile(request);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, equals('Profile updated successfully'));
        verify(
          mockApiClient.put('/auth/update-profile', data: request.toJson()),
        ).called(1);
      });
    });
  });
}
