import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:nextsportz_v2/features/auth/data/datasources/auth_datasource.dart';
import 'package:nextsportz_v2/features/auth/data/dto/auth_request_models.dart';
import 'package:nextsportz_v2/features/auth/data/dto/auth_response_models.dart';
import 'package:nextsportz_v2/core/local/token_storage.dart';

import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([AuthDatasource, TokenStorageService])
void main() {
  group('AuthRepositoryImpl', () {
    late MockAuthDatasource mockAuthDatasource;
    late MockTokenStorageService mockTokenStorage;
    late AuthRepositoryImpl authRepository;

    setUp(() {
      mockAuthDatasource = MockAuthDatasource();
      mockTokenStorage = MockTokenStorageService();
      authRepository = AuthRepositoryImpl(mockAuthDatasource, mockTokenStorage);
    });

    group('loginWithPhone', () {
      test('should return user when login is successful', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final loginRequest = LoginRequest(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        final userProfile = UserProfileResponse(
          userId: 1,
          fullName: 'Test User',
          email: '<EMAIL>',
          phoneNumber: phoneNumber,
          role: role,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final tokens = AuthTokenResponse(
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
        );

        final loginResponse = LoginResponse(tokens: tokens, user: userProfile);

        when(
          mockAuthDatasource.login(loginRequest),
        ).thenAnswer((_) async => loginResponse);
        when(
          mockTokenStorage.saveTokens(
            accessToken: tokens.accessToken!,
            refreshToken: tokens.refreshToken!,
          ),
        ).thenAnswer((_) async {});

        // Act
        final result = await authRepository.loginWithPhone(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold((error) => fail('Should not return error: $error'), (user) {
          expect(user.id, equals('1'));
          expect(user.name, equals('Test User'));
          expect(user.email, equals('<EMAIL>'));
          expect(user.phoneNumber, equals(phoneNumber));
          expect(user.role, equals(role));
        });

        verify(
          mockTokenStorage.saveTokens(
            accessToken: tokens.accessToken!,
            refreshToken: tokens.refreshToken!,
          ),
        ).called(1);
      });

      test('should return error when login fails', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'wrong_password';
        const role = 'PLAYER';

        final loginRequest = LoginRequest(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        final loginResponse = LoginResponse(tokens: null, user: null);

        when(
          mockAuthDatasource.login(loginRequest),
        ).thenAnswer((_) async => loginResponse);

        // Act
        final result = await authRepository.loginWithPhone(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals('Login failed')),
          (user) => fail('Should not return user'),
        );

        verifyNever(
          mockTokenStorage.saveTokens(
            accessToken: anyNamed('accessToken'),
            refreshToken: anyNamed('refreshToken'),
          ),
        );
      });

      test('should return error when datasource throws exception', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final loginRequest = LoginRequest(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        when(
          mockAuthDatasource.login(loginRequest),
        ).thenThrow(Exception('Network error'));

        // Act
        final result = await authRepository.loginWithPhone(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, contains('Network error')),
          (user) => fail('Should not return user'),
        );
      });
    });

    group('register', () {
      test('should register user successfully', () async {
        // Arrange
        const fullName = 'Test User';
        const email = '<EMAIL>';
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final registerRequest = RegisterRequest(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        final successResponse = SuccessResponse(
          success: true,
          message: 'User registered successfully',
        );

        when(
          mockAuthDatasource.register(registerRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.register(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );
      });

      test('should return error when registration fails', () async {
        // Arrange
        const fullName = 'Test User';
        const email = '<EMAIL>';
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final registerRequest = RegisterRequest(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        final successResponse = SuccessResponse(
          success: false,
          message: 'Registration failed',
        );

        when(
          mockAuthDatasource.register(registerRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.register(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals('Registration failed')),
          (success) => fail('Should not return success'),
        );
      });
    });

    group('verifyOtp', () {
      test('should verify OTP successfully', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const otp = '123456';

        final verifyRequest = RegisterVerifyRequest(
          phoneNumber: phoneNumber,
          otp: otp,
        );

        final successResponse = SuccessResponse(
          success: true,
          message: 'OTP verified successfully',
        );

        when(
          mockAuthDatasource.verifyOtp(verifyRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.verifyOtp(
          phoneNumber: phoneNumber,
          otp: otp,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );
      });
    });

    group('logout', () {
      test('should clear tokens on successful logout', () async {
        // Arrange
        final logoutResponse = SuccessResponse(
          success: true,
          message: 'Logged out',
        );

        when(
          mockAuthDatasource.logout(),
        ).thenAnswer((_) async => logoutResponse);
        when(mockTokenStorage.clearTokens()).thenAnswer((_) async {});

        // Act
        final result = await authRepository.logout();

        // Assert
        expect(result.isRight(), isTrue);
        verify(mockTokenStorage.clearTokens()).called(1);
      });

      test('should return error when logout fails', () async {
        // Arrange
        final logoutResponse = SuccessResponse(
          success: false,
          message: 'Logout failed',
        );

        when(
          mockAuthDatasource.logout(),
        ).thenAnswer((_) async => logoutResponse);

        // Act
        final result = await authRepository.logout();

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals('Logout failed')),
          (_) => fail('Should not return success'),
        );

        verifyNever(mockTokenStorage.clearTokens());
      });
    });

    group('isAuthenticated', () {
      test('should return true when user exists', () async {
        // Arrange
        final userProfile = UserProfileResponse(
          userId: 1,
          fullName: 'Test User',
          email: '<EMAIL>',
          phoneNumber: '1234567890',
          role: 'PLAYER',
        );

        when(
          mockAuthDatasource.getCurrentUser(),
        ).thenAnswer((_) async => userProfile);

        // Act
        final result = await authRepository.isAuthenticated();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (isAuthenticated) => expect(isAuthenticated, isTrue),
        );
      });

      test('should return false when user does not exist', () async {
        // Arrange
        final userProfile = const UserProfileResponse();

        when(
          mockAuthDatasource.getCurrentUser(),
        ).thenAnswer((_) async => userProfile);

        // Act
        final result = await authRepository.isAuthenticated();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (isAuthenticated) => expect(isAuthenticated, isFalse),
        );
      });
    });

    group('forgotPassword', () {
      test('should send forgot password email successfully', () async {
        // Arrange
        const email = '<EMAIL>';

        final forgotPasswordRequest = ForgotPasswordRequest(email: email);
        final successResponse = SuccessResponse(
          success: true,
          message: 'Password reset email sent',
        );

        when(
          mockAuthDatasource.forgotPassword(forgotPasswordRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.forgotPassword(email: email);

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );
      });
    });

    group('resetPassword', () {
      test('should reset password successfully', () async {
        // Arrange
        const token = 'reset_token';
        const newPassword = 'new_password123';

        final resetPasswordRequest = ResetPasswordRequest(
          token: token,
          newPassword: newPassword,
        );
        final successResponse = SuccessResponse(
          success: true,
          message: 'Password reset successfully',
        );

        when(
          mockAuthDatasource.resetPassword(resetPasswordRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.resetPassword(
          token: token,
          newPassword: newPassword,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );
      });
    });

    group('updateProfile', () {
      test('should update profile successfully', () async {
        // Arrange
        const fullName = 'Updated Name';
        const email = '<EMAIL>';

        final updateProfileRequest = UpdateProfileRequest(
          fullName: fullName,
          email: email,
        );
        final successResponse = SuccessResponse(
          success: true,
          message: 'Profile updated successfully',
        );

        when(
          mockAuthDatasource.updateProfile(updateProfileRequest),
        ).thenAnswer((_) async => successResponse);

        // Act
        final result = await authRepository.updateProfile(
          fullName: fullName,
          email: email,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );
      });
    });
  });
}
