import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/teams_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';

void main() {
  group('TeamsRepositoryImpl', () {
    late TeamsRepositoryImpl repository;

    setUp(() {
      repository = TeamsRepositoryImpl();
    });

    group('getMyTeams', () {
      test('should return list of teams', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result, isA<List<Team>>());
        expect(result.length, greaterThan(0));
        expect(result.first, isA<Team>());
        expect(result.first.name, isNotEmpty);
        expect(result.first.description, isNotEmpty);
        expect(result.first.createdBy, isNotEmpty);
        expect(result.first.isActive, isTrue);
      });

      test('should return teams with members', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.first.members, isA<List<TeamMember>>());
        expect(result.first.members.length, greaterThan(0));
        expect(result.first.members.first, isA<TeamMember>());
        expect(result.first.members.first.name, isNotEmpty);
        expect(result.first.members.first.position, isNotEmpty);
        expect(result.first.members.first.role, isNotEmpty);
      });

      test('should return teams with stats', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.first.stats, isA<TeamStats>());
        expect(result.first.stats.totalMatches, greaterThanOrEqualTo(0));
        expect(result.first.stats.wins, greaterThanOrEqualTo(0));
        expect(result.first.stats.losses, greaterThanOrEqualTo(0));
        expect(result.first.stats.draws, greaterThanOrEqualTo(0));
        expect(result.first.stats.winRate, greaterThanOrEqualTo(0.0));
      });
    });

    group('getTeamById', () {
      test('should return team by id', () async {
        // arrange
        final teams = await repository.getMyTeams();
        final teamId = teams.first.id;

        // act
        final result = await repository.getTeamById(teamId);

        // assert
        expect(result, isA<Team>());
        expect(result.id, teamId);
        expect(result.name, isNotEmpty);
      });

      test('should throw exception for non-existent team', () async {
        // act & assert
        expect(
          () => repository.getTeamById('non-existent-id'),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('createTeam', () {
      test('should create a new team', () async {
        // arrange
        const name = 'New Test Team';
        const description = 'A new test team description';

        // act
        final result = await repository.createTeam(
          name: name,
          description: description,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.name, name);
        expect(result.description, description);
        expect(result.createdBy, 'user1');
        expect(result.isActive, isTrue);
        expect(result.members.length, 1);
        expect(result.members.first.role, 'captain');
        expect(result.invitations, isEmpty);
      });

      test('should create team with logo', () async {
        // arrange
        const name = 'Team with Logo';
        const description = 'A team with logo';
        const logo = 'https://example.com/logo.png';

        // act
        final result = await repository.createTeam(
          name: name,
          description: description,
          logo: logo,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.name, name);
        expect(result.description, description);
        expect(result.logo, logo);
      });
    });

    group('updateTeam', () {
      test('should update team details', () async {
        // arrange
        final teams = await repository.getMyTeams();
        final teamId = teams.first.id;
        const newName = 'Updated Team Name';
        const newDescription = 'Updated description';

        // act
        final result = await repository.updateTeam(
          teamId: teamId,
          name: newName,
          description: newDescription,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.id, teamId);
        expect(result.name, newName);
        expect(result.description, newDescription);
      });
    });

    group('deleteTeam', () {
      test('should delete team without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';

        // act & assert
        expect(() => repository.deleteTeam(teamId), returnsNormally);
      });
    });

    group('invitePlayer', () {
      test('should invite player without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const message = 'Join our team!';

        // act & assert
        expect(
          () => repository.invitePlayer(
            teamId: teamId,
            playerId: playerId,
            message: message,
          ),
          returnsNormally,
        );
      });
    });

    group('acceptInvitation', () {
      test('should accept invitation without throwing exception', () async {
        // arrange
        const invitationId = 'test-invitation-id';

        // act & assert
        expect(
          () => repository.acceptInvitation(invitationId),
          returnsNormally,
        );
      });
    });

    group('declineInvitation', () {
      test('should decline invitation without throwing exception', () async {
        // arrange
        const invitationId = 'test-invitation-id';

        // act & assert
        expect(
          () => repository.declineInvitation(invitationId),
          returnsNormally,
        );
      });
    });

    group('removeMember', () {
      test('should remove member without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';

        // act & assert
        expect(
          () => repository.removeMember(teamId: teamId, memberId: memberId),
          returnsNormally,
        );
      });
    });

    group('updateMemberRole', () {
      test('should update member role without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';
        const newRole = 'vice_captain';

        // act & assert
        expect(
          () => repository.updateMemberRole(
            teamId: teamId,
            memberId: memberId,
            role: newRole,
          ),
          returnsNormally,
        );
      });
    });

    group('getPendingInvitations', () {
      test('should return list of pending invitations', () async {
        // act
        final result = await repository.getPendingInvitations();

        // assert
        expect(result, isA<List<TeamInvitation>>());
        expect(result.length, greaterThan(0));
        expect(result.first, isA<TeamInvitation>());
        expect(result.first.status, 'pending');
        expect(result.first.invitedUserName, isNotEmpty);
        expect(result.first.teamId, isNotEmpty);
      });
    });
  });
}
