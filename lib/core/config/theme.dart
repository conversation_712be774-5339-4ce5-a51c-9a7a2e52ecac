import 'package:flutter/material.dart';

class NextSportzTheme {
  // Light theme colors - Sporty Green & Orange Theme
  static const Color lightPrimary = Color(0xff0A2E1A); // Deep forest green
  static const Color lightSecondary = Color(0xff1A472A); // Medium green
  static const Color lightAccent = Color(0xffFF6B35); // Vibrant orange
  static const Color lightAccentSecondary = Color(0xffFF8E53); // Light orange
  static const Color lightBlue = Color(0xff2D5A3D); // Green-blue
  static const Color lightMediumBlue = Color(0xff274A35); // Darker green
  static const Color lightGrey = Color(0xff7A8C7D); // Green-tinted grey
  static const Color lightLightGrey = Color(0xff3E5A47); // Light green-grey

  // Dark theme colors - Sporty Dark Theme
  static const Color darkPrimary = Color(0xff0A1A0F); // Very dark green
  static const Color darkSecondary = Color(0xff1A2D1F); // Dark green
  static const Color darkAccent = Color(0xffFF6B35); // Same vibrant orange
  static const Color darkAccentSecondary =
      Color(0xffFF8E53); // Same light orange
  static const Color darkBlue = Color(0xff1A2D1F); // Dark green
  static const Color darkMediumBlue = Color(0xff223A28); // Medium dark green
  static const Color darkGrey = Color(0xffB8C5BB); // Light green-grey
  static const Color darkLightGrey = Color(0xff2A3D2F); // Medium green-grey

  // Legacy color references (for backward compatibility)
  static const Color primary = lightPrimary;
  static const Color secondary = lightSecondary;
  static const Color accent = lightAccent;
  static const Color accentSecondary = lightAccentSecondary;
  static const Color mediumBlue = lightMediumBlue;
  static const Color grey = lightGrey;

  // Sporty gradient definitions
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [Color(0xffFF6B35), Color(0xffFF8E53), Color(0xffFF6B35)],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0x40FFFFFF), Color(0x20FFFFFF)],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xff0A2E1A), Color(0xff1A472A)],
  );

  // Sporty accent gradients
  static const LinearGradient sportyGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xffFF6B35), Color(0xffFF8E53), Color(0xff4CAF50)],
  );

  static const LinearGradient energyGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [Color(0xff4CAF50), Color(0xffFF6B35), Color(0xffFF8E53)],
  );

  // Text styles
  static const TextStyle titleStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 14,
  );

  static const TextStyle hintStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Color(0xff7A8C7D),
    fontSize: 12,
  );

  static const TextStyle buttonStyle = TextStyle(
    fontFamily: 'Gilroy_Medium',
    color: Colors.white,
    fontSize: 15,
    fontWeight: FontWeight.bold,
  );

  // Input decoration theme
  static InputDecoration getInputDecoration({
    required String hintText,
    required IconData prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
  }) {
    return InputDecoration(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(30),
        borderSide: const BorderSide(width: 0, style: BorderStyle.none),
      ),
      fillColor: lightBlue,
      hintStyle: hintStyle,
      hintText: hintText,
      filled: true,
      prefixIcon: Icon(prefixIcon, color: Colors.white, size: 20),
      suffixIcon: suffixIcon != null
          ? GestureDetector(
              onTap: onSuffixTap,
              child: Icon(suffixIcon, color: grey, size: 20),
            )
          : null,
    );
  }

  // Button decoration with sporty gradient
  static BoxDecoration getButtonDecoration() {
    return const BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(20)),
      gradient: sportyGradient,
    );
  }

  // Card decoration with enhanced sporty look
  static BoxDecoration getCardDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(15),
      gradient: cardGradient,
      border: Border.all(color: Colors.white.withOpacity(0.15), width: 1),
      boxShadow: [
        BoxShadow(
          color: const Color(0xffFF6B35).withOpacity(0.1),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  // Sporty card decoration with accent border
  static BoxDecoration getSportyCardDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(15),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xff1A472A).withOpacity(0.8),
          const Color(0xff2D5A3D).withOpacity(0.6),
        ],
      ),
      border: Border.all(
        color: const Color(0xffFF6B35).withOpacity(0.3),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: const Color(0xffFF6B35).withOpacity(0.15),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.light,
      primaryColor: lightPrimary,
      scaffoldBackgroundColor: lightPrimary,
      fontFamily: 'Gilroy_Medium',
      colorScheme: const ColorScheme.light(
        primary: lightAccent,
        secondary: lightAccentSecondary,
        surface: lightSecondary,
        background: lightPrimary,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onBackground: Colors.white,
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: lightPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Gilroy_Bold',
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      shadowColor: Colors.transparent,
      splashColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      dividerColor: Colors.transparent,
    );
  }

  // Dark theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: false,
      brightness: Brightness.dark,
      primaryColor: darkPrimary,
      scaffoldBackgroundColor: darkPrimary,
      fontFamily: 'Gilroy_Medium',
      colorScheme: const ColorScheme.dark(
        primary: darkAccent,
        secondary: darkAccentSecondary,
        surface: darkSecondary,
        background: darkPrimary,
        error: Colors.red,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onBackground: Colors.white,
        onError: Colors.white,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: darkPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Gilroy_Bold',
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      shadowColor: Colors.transparent,
      splashColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      dividerColor: Colors.transparent,
    );
  }

  // Get current colors based on theme mode
  static ColorScheme getColors(bool isDark) {
    return isDark ? darkTheme.colorScheme : lightTheme.colorScheme;
  }

  static Color getPrimaryColor(bool isDark) {
    return isDark ? darkPrimary : lightPrimary;
  }

  static Color getSecondaryColor(bool isDark) {
    return isDark ? darkSecondary : lightSecondary;
  }

  static Color getAccentColor(bool isDark) {
    return isDark ? darkAccent : lightAccent;
  }

  static Color getGreyColor(bool isDark) {
    return isDark ? darkGrey : lightGrey;
  }

  static Color getLightGreyColor(bool isDark) {
    return isDark ? darkLightGrey : lightLightGrey;
  }
}
