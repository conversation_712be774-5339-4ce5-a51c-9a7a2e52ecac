import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../utils/color_notifire.dart';

class SecondScreen extends StatefulWidget {
  const SecondScreen({Key? key}) : super(key: key);

  @override
  _SecondScreenState createState() => _SecondScreenState();
}

class _SecondScreenState extends State<SecondScreen> {
  final int _numPages = 3;

  late ColorNotifire notifire;
  final PageController _pageController = PageController(initialPage: 0);
  int _currentPage = 0;

  List<Widget> _buildPageIndicator() {
    List<Widget> list = [];
    for (int i = 0; i < _numPages; i++) {
      list.add(i == _currentPage ? _indicator(true) : _indicator(false));
    }
    return list;
  }

  Widget _indicator(bool isActive) {
    return AnimatedContainer(
      duration: const Duration(microseconds: 150),
      margin: const EdgeInsets.symmetric(horizontal: 3.0),
      height: 8.0,
      width: isActive ? 24.0 : 8.0,
      decoration: BoxDecoration(
        color: isActive ? const Color(0xff22c0ff) : const Color(0xff474e84),
        borderRadius: const BorderRadius.all(Radius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    notifire = Provider.of<ColorNotifire>(context, listen: true);
    return Scaffold(
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.light,
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xff030A75), Color(0xff000000)],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                color: Colors.transparent,
                height: MediaQuery.of(context).size.height / 1.2,
                child: PageView(
                  physics: const ClampingScrollPhysics(),
                  controller: _pageController,
                  onPageChanged: (int page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  children: <Widget>[
                    _buildPage1(),
                    _buildPage2(),
                    _buildPage3(),
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildPageIndicator(),
              ),
              _buildBottomNavigation(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPage1() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Center(
            child: Container(
              color: Colors.transparent,
              child: Image.asset(
                'assets/images/onboarding_1.png',
                fit: BoxFit.cover,
                height: MediaQuery.of(context).size.height / 1.5,
                width: MediaQuery.of(context).size.width,
              ),
            ),
          ),
          Center(
            child: Column(
              children: [
                const Text(
                  "Your World of Sports",
                  style: TextStyle(
                    fontFamily: 'Gilroy Bold',
                    color: Color(0xfffefeff),
                    fontSize: 30,
                  ),
                ),
                const SizedBox(height: 15),
                const Text(
                  "Connect with soccer players, join tournaments,",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "and find the perfect venue for your next match!",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage2() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Center(
            child: Container(
              color: Colors.transparent,
              child: Image.asset(
                'assets/images/onboarding_2.png',
                fit: BoxFit.cover,
                height: MediaQuery.of(context).size.height / 1.5,
                width: MediaQuery.of(context).size.width,
              ),
            ),
          ),
          Center(
            child: Column(
              children: [
                const Text(
                  "It's time to Play",
                  style: TextStyle(
                    fontFamily: 'Gilroy Bold',
                    color: Color(0xfffefeff),
                    fontSize: 30,
                  ),
                ),
                const SizedBox(height: 15),
                const Text(
                  "With all the information you need,",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "you can start playing and winning.",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "Good Luck!",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage3() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Center(
            child: Container(
              color: Colors.transparent,
              child: Image.asset(
                'assets/images/onboarding_3.png',
                fit: BoxFit.cover,
                height: MediaQuery.of(context).size.height / 1.5,
                width: MediaQuery.of(context).size.width,
              ),
            ),
          ),
          Center(
            child: Column(
              children: [
                const Text(
                  "Win Glorious prizes!",
                  style: TextStyle(
                    color: Color(0xfffefeff),
                    fontSize: 30,
                    fontFamily: 'Gilroy Bold',
                  ),
                ),
                const SizedBox(height: 15),
                const Text(
                  "Ready to challenge players from all",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "over the world in the ultimate sports",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "contest? Join tournaments and compete",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
                const Text(
                  "for amazing prizes!",
                  style: TextStyle(
                    fontFamily: 'Gilroy Medium',
                    color: Color(0xfffefeff),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Expanded(
      child: Align(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  context.go('/login');
                },
                child: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.0),
                  child: Text(
                    'Skip this',
                    style: TextStyle(
                      fontFamily: 'Gilroy Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (_currentPage == _numPages - 1) {
                    context.go('/login');
                  } else {
                    _pageController.nextPage(
                      duration: const Duration(microseconds: 300),
                      curve: Curves.easeIn,
                    );
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Container(
                    height: MediaQuery.of(context).size.height / 25,
                    width: MediaQuery.of(context).size.width / 4,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: [
                          Color(0xffDA22FF),
                          Color(0xff9733EE),
                          Color(0xffDA22FF),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Text(
                        _currentPage == _numPages - 1 ? "Start" : "Next",
                        style: const TextStyle(
                          fontFamily: 'Gilroy Medium',
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
