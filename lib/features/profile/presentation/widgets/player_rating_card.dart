import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../utils/color.dart';
import '../screens/player_rating_details_screen.dart';
import '../../../player/player_providers.dart';
import '../../../player/presentation/screens/self_assessment_dialog_screen.dart';

class PlayerRatingCard extends ConsumerWidget {
  const PlayerRatingCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use current user's player rating data
    final playerRatingAsync = ref.watch(currentPlayerRatingProvider);

    return GestureDetector(
      onTap: () async {
        // Get current player ID and navigate based on assessment status
        final playerId = await ref.read(currentPlayerIdProvider.future);
        if (context.mounted) {
          final ratingResult = playerRatingAsync.value;
          if (ratingResult != null) {
            ratingResult.fold(
              (error) {
                // If there's an error, still try to open self-assessment
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) =>
                        SelfAssessmentDialogScreen(playerId: playerId),
                  ),
                );
              },
              (rating) {
                if (rating.hasSelfAssessment) {
                  // If assessment is completed, show details screen
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          PlayerRatingDetailsScreen(playerId: playerId),
                    ),
                  );
                } else {
                  // If no assessment, open self-assessment dialog
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          SelfAssessmentDialogScreen(playerId: playerId),
                    ),
                  );
                }
              },
            );
          } else {
            // If no data yet, open self-assessment dialog
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    SelfAssessmentDialogScreen(playerId: playerId),
              ),
            );
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xffDA22FF).withOpacity(0.1),
              lightblue.withOpacity(0.3),
            ],
          ),
          border: Border.all(
            color: const Color(0xffDA22FF).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xffDA22FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Ratings',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 18,
                        ),
                      ),
                      Text(
                        'Your performance ratings',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // Rating chip beside the title
                playerRatingAsync.when(
                  data: (result) => result.fold(
                    (error) => const SizedBox.shrink(),
                    (rating) {
                      if (!rating.hasSelfAssessment) {
                        return const SizedBox.shrink();
                      }
                      final displayRating =
                          (rating.averageRating / 10).toStringAsFixed(1);
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xffDA22FF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: const Color(0xffDA22FF).withOpacity(0.5),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Color(0xffDA22FF),
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '$displayRating/10',
                              style: const TextStyle(
                                fontFamily: 'Gilroy_Bold',
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  loading: () => _buildShimmerChip(),
                  error: (error, stack) => const SizedBox.shrink(),
                ),
              ],
            ),

            // Show assessment prompt only if no self-assessment
            playerRatingAsync.when(
              data: (result) => result.fold(
                (error) => _buildNoRatingPrompt(),
                (rating) => rating.hasSelfAssessment
                    ? const SizedBox.shrink()
                    : _buildNoRatingPrompt(),
              ),
              loading: () => _buildShimmerLoading(),
              error: (error, stack) => _buildNoRatingPrompt(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoRatingPrompt() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.assessment,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'Complete Your Assessment',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Rate your skills across 6 key attributes to get your personalized performance rating.',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.grey,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.blue.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.rate_review,
                  color: Colors.blue,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Start Assessment',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.blue,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerChip() {
    return Shimmer.fromColors(
      baseColor: lightblue.withOpacity(0.3),
      highlightColor: lightblue.withOpacity(0.1),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 14,
              height: 14,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: 40,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: lightblue.withOpacity(0.3),
      highlightColor: lightblue.withOpacity(0.1),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 120,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
