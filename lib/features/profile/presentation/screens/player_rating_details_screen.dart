import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../../../player/player_providers.dart';
import '../../../player/domain/entities/player_rating.dart';
import '../../../player/domain/entities/player_rating.dart' as player_rating;

class PlayerRatingDetailsScreen extends ConsumerWidget {
  final String playerId;

  const PlayerRatingDetailsScreen({
    super.key,
    required this.playerId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final playerRatingAsync = ref.watch(playerRatingProvider(playerId));

    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        title: const Text(
          'Assessment Results',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SafeArea(
          child: playerRatingAsync.when(
            data: (result) => result.fold(
              (error) => _buildErrorWidget(error.message),
              (rating) => _buildContent(context, rating),
            ),
            loading: () => const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            error: (error, stack) => _buildErrorWidget(error.toString()),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, PlayerRating rating) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with Overall Rating
            _buildHeaderCard(rating),

            const SizedBox(height: 24),

            // Performance Charts
            _buildPerformanceCharts(rating),

            const SizedBox(height: 24),

            // Recent Assessments
            if (rating.recentVotes.isNotEmpty) _buildRecentAssessments(rating),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xffDA22FF).withOpacity(0.1),
            lightblue.withOpacity(0.3),
          ],
        ),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xffDA22FF),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your Performance Rating',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        fontSize: 20,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Based on ${rating.totalRatings} assessment${rating.totalRatings == 1 ? '' : 's'}',
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                (rating.averageRating / 10).toStringAsFixed(1),
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 48,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '/10',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    'Overall',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceCharts(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: lightblue.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xffDA22FF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.analytics,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Performance Breakdown',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ..._buildAttributeBars(rating),
        ],
      ),
    );
  }

  List<Widget> _buildAttributeBars(PlayerRating rating) {
    final attributes = [
      {'name': 'Defense', 'value': rating.defense, 'icon': Icons.shield},
      {
        'name': 'Shooting',
        'value': rating.shooting,
        'icon': Icons.sports_soccer
      },
      {'name': 'Passing', 'value': rating.passing, 'icon': Icons.swap_horiz},
      {'name': 'Pace', 'value': rating.pace, 'icon': Icons.speed},
      {
        'name': 'Physicality',
        'value': rating.physicality,
        'icon': Icons.fitness_center
      },
      {
        'name': 'Dribbling',
        'value': rating.dribbling,
        'icon': Icons.track_changes
      },
    ];

    return attributes
        .map((attr) => _buildAttributeBar(
              attr['name'] as String,
              attr['value'] as double,
              attr['icon'] as IconData,
            ))
        .toList();
  }

  Widget _buildAttributeBar(String name, double value, IconData icon) {
    final percentage = (value / 100) * 100;
    final color = _getRatingColor(value);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 8),
              Text(
                name,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                '${value.round()}',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 14,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Container(
            height: 8,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentAssessments(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xffDA22FF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.history,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Recent Assessments',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...rating.recentVotes
              .take(5)
              .map((vote) => _buildAssessmentItem(vote)),
        ],
      ),
    );
  }

  Widget _buildAssessmentItem(player_rating.PlayerRatingVote vote) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${vote.averageRating.toStringAsFixed(1)}★',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  vote.voterName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ),
              Text(
                _formatDate(vote.createdAt),
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white54,
                ),
              ),
            ],
          ),
          if (vote.comments != null && vote.comments!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              vote.comments!,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.white70,
              ),
            ),
          ],
          const SizedBox(height: 8),
          _buildAttributeGrid(vote),
        ],
      ),
    );
  }

  Widget _buildAttributeGrid(player_rating.PlayerRatingVote vote) {
    final attributes = [
      {'name': 'Def', 'value': vote.defense.toDouble()},
      {'name': 'Shoot', 'value': vote.shooting.toDouble()},
      {'name': 'Pass', 'value': vote.passing.toDouble()},
      {'name': 'Pace', 'value': vote.pace.toDouble()},
      {'name': 'Phys', 'value': vote.physicality.toDouble()},
      {'name': 'Drib', 'value': vote.dribbling.toDouble()},
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: attributes
          .map((attr) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color:
                      _getRatingColor(attr['value'] as double).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _getRatingColor(attr['value'] as double)
                        .withOpacity(0.5),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${attr['name']}: ${(attr['value'] as double).round()}',
                  style: TextStyle(
                    fontSize: 10,
                    color: _getRatingColor(attr['value'] as double),
                    fontFamily: 'Gilroy_Medium',
                  ),
                ),
              ))
          .toList(),
    );
  }

  Color _getRatingColor(double rating) {
    if (rating >= 80) return Colors.green;
    if (rating >= 60) return Colors.orange;
    if (rating >= 40) return Colors.yellow[700]!;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading assessment data',
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              fontFamily: 'Gilroy_Medium',
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
