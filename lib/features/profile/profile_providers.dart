import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'domain/entities/profile.dart';
import 'data/repositories/profile_repository_impl.dart';
import 'data/datasources/profile_remote_datasource.dart';
import '../../core/networking/api_client.dart';

// Profile datasource provider
final profileDatasourceProvider = Provider<ProfileRemoteDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return ProfileRemoteDataSource(apiClient);
});

// Profile repository provider
final profileRepositoryProvider = Provider<ProfileRepositoryImpl>((ref) {
  final datasource = ref.read(profileDatasourceProvider);
  return ProfileRepositoryImpl(datasource);
});

// Profile state notifier
class ProfileNotifier extends StateNotifier<AsyncValue<Profile?>> {
  final ProfileRepositoryImpl _repository;

  ProfileNotifier(this._repository) : super(const AsyncValue.loading());

  Future<void> loadProfile() async {
    try {
      state = const AsyncValue.loading();

      final result = await _repository.getProfile('current_user');

      result.fold(
        (error) => state = AsyncValue.error(error, StackTrace.current),
        (profile) => state = AsyncValue.data(profile),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateProfile(Profile updatedProfile) async {
    try {
      state = const AsyncValue.loading();

      final result = await _repository.updateProfile(updatedProfile);

      result.fold(
        (error) => state = AsyncValue.error(error, StackTrace.current),
        (profile) => state = AsyncValue.data(profile),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> uploadProfileImage(String imagePath) async {
    try {
      final result =
          await _repository.updateProfileImage('current_user', imagePath);

      result.fold(
        (error) => state = AsyncValue.error(error, StackTrace.current),
        (_) => loadProfile(), // Reload profile after image upload
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> logout() async {
    try {
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Profile provider
final profileProvider =
    StateNotifierProvider<ProfileNotifier, AsyncValue<Profile?>>(
  (ref) => ProfileNotifier(ref.read(profileRepositoryProvider)),
);

// Profile stats provider
final profileStatsProvider = Provider<Map<String, dynamic>>((ref) {
  return {
    'matches': 45,
    'wins': 32,
    'winRate': '71%',
    'rating': '4.8',
    'challengesCompleted': 28,
    'teams': 3,
  };
});

// Profile menu items provider
final profileMenuItemsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return [
    {
      'title': 'Performance Stats',
      'icon': 'analytics',
      'route': '/performance',
    },
    {'title': 'Challenges', 'icon': 'sports_soccer', 'route': '/challenges'},
    {'title': 'Tournaments', 'icon': 'emoji_events', 'route': '/tournaments'},
    {'title': 'My Teams', 'icon': 'group', 'route': '/teams'},
    {'title': 'Booked Venues', 'icon': 'location_on', 'route': '/venues'},
    {'title': 'Settings', 'icon': 'settings', 'route': '/settings'},
    {'title': 'Help & Support', 'icon': 'help_outline', 'route': '/help'},
  ];
});
