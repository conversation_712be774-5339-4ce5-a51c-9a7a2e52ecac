import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/providers.dart';
import '../../core/networking/api_client.dart';
import '../../core/local/key_value_storage.dart';
import 'data/datasources/teams_remote_datasource.dart';
import 'data/datasources/teams_local_datasource.dart';
import 'data/repositories/teams_repository_impl.dart';
import 'domain/entities/team.dart';
import 'domain/repositories/teams_repository.dart';
import 'domain/usecases/teams_usecases.dart';

// Data source providers
final teamsRemoteDataSourceProvider = Provider<TeamsRemoteDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return TeamsRemoteDataSourceImpl(apiClient);
});

final teamsLocalDataSourceProvider = Provider<TeamsLocalDataSource>((ref) {
  final storage = ref.read(keyValueStorageProvider);
  final adapter = KeyValueStorageAdapter(storage);
  return TeamsLocalDataSourceImpl(adapter);
});

// Repository provider
final teamsRepositoryProvider = Provider<TeamsRepository>((ref) {
  final remoteDataSource = ref.read(teamsRemoteDataSourceProvider);
  final localDataSource = ref.read(teamsLocalDataSourceProvider);
  return TeamsRepositoryImpl(remoteDataSource, localDataSource);
});

// Use cases providers
final getMyTeamsUseCaseProvider = Provider<GetMyTeamsUseCase>((ref) {
  return GetMyTeamsUseCase(ref.read(teamsRepositoryProvider));
});

final getTeamByIdUseCaseProvider = Provider<GetTeamByIdUseCase>((ref) {
  return GetTeamByIdUseCase(ref.read(teamsRepositoryProvider));
});

final createTeamUseCaseProvider = Provider<CreateTeamUseCase>((ref) {
  return CreateTeamUseCase(ref.read(teamsRepositoryProvider));
});

final updateTeamUseCaseProvider = Provider<UpdateTeamUseCase>((ref) {
  return UpdateTeamUseCase(ref.read(teamsRepositoryProvider));
});

final deleteTeamUseCaseProvider = Provider<DeleteTeamUseCase>((ref) {
  return DeleteTeamUseCase(ref.read(teamsRepositoryProvider));
});

final invitePlayerUseCaseProvider = Provider<InvitePlayerUseCase>((ref) {
  return InvitePlayerUseCase(ref.read(teamsRepositoryProvider));
});

final acceptInvitationUseCaseProvider = Provider<AcceptInvitationUseCase>((
  ref,
) {
  return AcceptInvitationUseCase(ref.read(teamsRepositoryProvider));
});

final declineInvitationUseCaseProvider = Provider<DeclineInvitationUseCase>((
  ref,
) {
  return DeclineInvitationUseCase(ref.read(teamsRepositoryProvider));
});

final removeMemberUseCaseProvider = Provider<RemoveMemberUseCase>((ref) {
  return RemoveMemberUseCase(ref.read(teamsRepositoryProvider));
});

final updateMemberRoleUseCaseProvider = Provider<UpdateMemberRoleUseCase>((
  ref,
) {
  return UpdateMemberRoleUseCase(ref.read(teamsRepositoryProvider));
});

final getPendingInvitationsUseCaseProvider =
    Provider<GetPendingInvitationsUseCase>((ref) {
  return GetPendingInvitationsUseCase(ref.read(teamsRepositoryProvider));
});

// State providers
final myTeamsProvider = FutureProvider.autoDispose<List<Team>>((ref) async {
  final useCase = ref.read(getMyTeamsUseCaseProvider);
  return await useCase();
});

final pendingInvitationsProvider =
    FutureProvider.autoDispose<List<TeamInvitation>>((ref) async {
  final useCase = ref.read(getPendingInvitationsUseCaseProvider);
  return await useCase();
});

final teamByIdProvider = FutureProvider.family<Team, String>((
  ref,
  teamId,
) async {
  final useCase = ref.read(getTeamByIdUseCaseProvider);
  return await useCase(teamId);
});
