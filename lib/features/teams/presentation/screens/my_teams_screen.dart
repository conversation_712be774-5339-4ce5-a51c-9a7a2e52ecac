import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../utils/color.dart';
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import 'create_team_screen.dart';
import 'team_details_screen.dart';

class MyTeamsScreen extends ConsumerStatefulWidget {
  const MyTeamsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<MyTeamsScreen> createState() => _MyTeamsScreenState();
}

class _MyTeamsScreenState extends ConsumerState<MyTeamsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'My Teams',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreateTeamScreen(),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xffDA22FF),
          labelColor: Colors.white,
          unselectedLabelColor: grey,
          labelStyle: const TextStyle(fontFamily: 'Gilroy_Bold', fontSize: 16),
          unselectedLabelStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            fontSize: 16,
          ),
          tabs: const [Tab(text: 'My Teams'), Tab(text: 'Invitations')],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildMyTeamsTab(), _buildInvitationsTab()],
      ),
    );
  }

  Widget _buildMyTeamsTab() {
    final teamsAsync = ref.watch(myTeamsProvider);

    return teamsAsync.when(
      data:
          (teams) =>
              teams.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: teams.length,
                    itemBuilder: (context, index) {
                      final team = teams[index];
                      return _buildTeamCard(team);
                    },
                  ),
      loading:
          () => const Center(
            child: CircularProgressIndicator(color: Color(0xffDA22FF)),
          ),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                Text(
                  'Error loading teams',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => ref.refresh(myTeamsProvider),
                  child: const Text(
                    'Retry',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Color(0xffDA22FF),
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildInvitationsTab() {
    final invitationsAsync = ref.watch(pendingInvitationsProvider);

    return invitationsAsync.when(
      data: (invitations) {
        return invitations.isEmpty
            ? _buildEmptyInvitationsState()
            : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: invitations.length,
              itemBuilder: (context, index) {
                final invitation = invitations[index];
                return _buildReceivedInvitationCard(invitation);
              },
            );
      },
      loading:
          () => const Center(
            child: CircularProgressIndicator(color: Color(0xffDA22FF)),
          ),
              error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                Text(
                  'Error loading invitations',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => ref.refresh(pendingInvitationsProvider),
                  child: const Text(
                    'Retry',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Color(0xffDA22FF),
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildTeamCard(Team team) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TeamDetailsScreen(teamId: team.id),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: const Color(0xffDA22FF).withOpacity(0.2),
                    ),
                    child:
                        team.logo != null
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(15),
                              child: CachedNetworkImage(
                                imageUrl: team.logo!,
                                fit: BoxFit.cover,
                                width: 60,
                                height: 60,
                                placeholder:
                                    (context, url) => Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                          color: Color(0xffDA22FF),
                                          strokeWidth: 2,
                                        ),
                                      ),
                                    ),
                                errorWidget:
                                    (context, url, error) => Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: const Icon(
                                        Icons.sports_soccer,
                                        color: Colors.white,
                                        size: 30,
                                      ),
                                    ),
                              ),
                            )
                            : const Icon(
                              Icons.sports_soccer,
                              color: Color(0xffDA22FF),
                              size: 30,
                            ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          team.name,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Football Team',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: grey,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.arrow_forward_ios, color: grey, size: 16),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                team.description,
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    'Members',
                    '${team.members.length}',
                    Icons.people,
                  ),
                  _buildStatItem(
                    'Matches',
                    '${team.stats.totalMatches}',
                    Icons.sports_soccer,
                  ),
                  _buildStatItem(
                    'Win Rate',
                    '${team.stats.winRate.toStringAsFixed(0)}%',
                    Icons.trending_up,
                  ),
                ],
              ),
              if (team.invitations.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.orange.withOpacity(0.2),
                  ),
                  child: Text(
                    '${team.invitations.length} pending invitation${team.invitations.length > 1 ? 's' : ''}',
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.orange,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReceivedInvitationCard(TeamInvitation invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 25,
                  backgroundColor: const Color(0xffDA22FF).withOpacity(0.2),
                  child: Text(
                    invitation.invitedUserName[0].toUpperCase(),
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Bold',
                      color: Color(0xffDA22FF),
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.invitedUserName,
                        style: const TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Invited ${_formatDate(invitation.createdAt)}',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.orange.withOpacity(0.2),
                  ),
                  child: Text(
                    'Pending',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.orange,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptInvitation(invitation.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xffDA22FF),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Accept',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineInvitation(invitation.id),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Decline',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xffDA22FF), size: 16),
        const SizedBox(width: 3),
        Text(
          '$value',
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 14,
          ),
        ),
        const SizedBox(width: 3),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
              color: const Color(0xffDA22FF).withOpacity(0.2),
            ),
            child: const Icon(Icons.group, color: Color(0xffDA22FF), size: 60),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Teams Yet',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first team to get started',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreateTeamScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xffDA22FF),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text(
              'Create Team',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInvitationsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
              color: const Color(0xffDA22FF).withOpacity(0.2),
            ),
            child: const Icon(
              Icons.mail_outline,
              color: Color(0xffDA22FF),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Pending Invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You don\'t have any pending team invitations',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _acceptInvitation(String invitationId) async {
    try {
      final useCase = ref.read(acceptInvitationUseCaseProvider);
      await useCase(invitationId);
      
      // Refresh the providers to update the UI
      ref.invalidate(pendingInvitationsProvider);
      ref.invalidate(myTeamsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation accepted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accepting invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _declineInvitation(String invitationId) async {
    try {
      final useCase = ref.read(declineInvitationUseCaseProvider);
      await useCase(invitationId);
      
      // Refresh the provider to update the UI
      ref.invalidate(pendingInvitationsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation declined'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error declining invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }


}
