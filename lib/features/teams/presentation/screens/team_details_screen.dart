import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../utils/color.dart';
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import 'invite_player_screen.dart';

class TeamDetailsScreen extends ConsumerStatefulWidget {
  final String teamId;

  const TeamDetailsScreen({Key? key, required this.teamId}) : super(key: key);

  @override
  ConsumerState<TeamDetailsScreen> createState() => _TeamDetailsScreenState();
}

class _TeamDetailsScreenState extends ConsumerState<TeamDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final teamAsync = ref.watch(teamByIdProvider(widget.teamId));

    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: teamAsync.when(
          data:
              (team) => Text(
                team.name,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          loading:
              () => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          error:
              (_, __) => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => InvitePlayerScreen(teamId: widget.teamId),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xffDA22FF),
          labelColor: Colors.white,
          unselectedLabelColor: grey,
          labelStyle: const TextStyle(fontFamily: 'Gilroy_Bold', fontSize: 14),
          unselectedLabelStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            fontSize: 14,
          ),
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Members'),
            Tab(text: 'Stats'),
          ],
        ),
      ),
      body: teamAsync.when(
        data:
            (team) => TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(team),
                _buildMembersTab(team),
                _buildStatsTab(team),
              ],
            ),
        loading:
            () => const Center(
              child: CircularProgressIndicator(color: Color(0xffDA22FF)),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading team',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed:
                        () => ref.refresh(teamByIdProvider(widget.teamId)),
                    child: const Text(
                      'Retry',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Color(0xffDA22FF),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildOverviewTab(Team team) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Team Header
          Center(
            child: Column(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    color: const Color(0xffDA22FF).withOpacity(0.2),
                  ),
                  child:
                      team.logo != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(50),
                            child: CachedNetworkImage(
                              imageUrl: team.logo!,
                              fit: BoxFit.cover,
                              width: 100,
                              height: 100,
                              placeholder:
                                  (context, url) => Container(
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        color: Color(0xffDA22FF),
                                      ),
                                    ),
                                  ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    width: 100,
                                    height: 100,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                    child: const Icon(
                                      Icons.sports_soccer,
                                      color: Colors.white,
                                      size: 50,
                                    ),
                                  ),
                            ),
                          )
                          : const Icon(
                            Icons.sports_soccer,
                            color: Color(0xffDA22FF),
                            size: 50,
                          ),
                ),
                const SizedBox(height: 16),
                Text(
                  team.name,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Football Team',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Description
          _buildSection(
            'About',
            Icons.description,
            Text(
              team.description,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.8),
                fontSize: 16,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick Stats
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: lightblue.withOpacity(0.5),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickStat(
                    'Members',
                    '${team.members.length}',
                    Icons.people,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Matches',
                    '${team.stats.totalMatches}',
                    Icons.sports_soccer,
                  ),
                ),
                Expanded(
                  child: _buildQuickStat(
                    'Win Rate',
                    '${team.stats.winRate.toStringAsFixed(0)}%',
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Team Info
          _buildSection(
            'Team Info',
            Icons.info,
            Column(
              children: [
                _buildInfoRow('Created', _formatDate(team.createdAt)),
                _buildInfoRow('Last Updated', _formatDate(team.updatedAt)),
                _buildInfoRow('Status', team.isActive ? 'Active' : 'Inactive'),
              ],
            ),
          ),

          if (team.invitations.isNotEmpty) ...[
            const SizedBox(height: 24),
            _buildSection(
              'Pending Invitations',
              Icons.mail_outline,
              Column(
                children:
                    team.invitations.map((invitation) {
                      return _buildInvitationItem(invitation);
                    }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMembersTab(Team team) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: team.members.length,
      itemBuilder: (context, index) {
        final member = team.members[index];
        return _buildMemberCard(member);
      },
    );
  }

  Widget _buildStatsTab(Team team) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Stats
          _buildSection(
            'Overall Performance',
            Icons.analytics,
            Column(
              children: [
                _buildStatRow('Total Matches', '${team.stats.totalMatches}'),
                _buildStatRow('Wins', '${team.stats.wins}'),
                _buildStatRow('Losses', '${team.stats.losses}'),
                _buildStatRow('Draws', '${team.stats.draws}'),
                _buildStatRow(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(1)}%',
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Goals Stats
          _buildSection(
            'Goals',
            Icons.sports_soccer,
            Column(
              children: [
                _buildStatRow('Total Goals', '${team.stats.totalGoals}'),
                _buildStatRow('Goals Conceded', '${team.stats.goalsConceded}'),
                _buildStatRow('Clean Sheets', '${team.stats.cleanSheets}'),
                _buildStatRow(
                  'Avg Goals/Match',
                  '${team.stats.averageGoalsPerMatch.toStringAsFixed(1)}',
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Top Performers
          _buildSection(
            'Top Performers',
            Icons.star,
            Column(
              children: [
                ...team.members
                    .take(3)
                    .map((member) => _buildTopPerformerCard(member))
                    .toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xffDA22FF), size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xffDA22FF), size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberCard(TeamMember member) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: const Color(0xffDA22FF).withOpacity(0.2),
            child:
                member.profileImage != null
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: CachedNetworkImage(
                        imageUrl: member.profileImage!,
                        fit: BoxFit.cover,
                        width: 60,
                        height: 60,
                        placeholder:
                            (context, url) => Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Color(0xffDA22FF),
                                  strokeWidth: 2,
                                ),
                              ),
                            ),
                        errorWidget:
                            (context, url, error) => Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(30),
                              ),
                              child: const Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                      ),
                    )
                    : Text(
                      member.name[0].toUpperCase(),
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Color(0xffDA22FF),
                        fontSize: 20,
                      ),
                    ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.name,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  member.position,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: _getRoleColor(member.role).withOpacity(0.2),
                  ),
                  child: Text(
                    _getRoleDisplayName(member.role),
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: _getRoleColor(member.role),
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${member.stats.rating}',
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              Text(
                'Rating',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: grey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationItem(TeamInvitation invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.orange.withOpacity(0.1),
        border: Border.all(color: Colors.orange.withOpacity(0.3), width: 1),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.orange.withOpacity(0.2),
            child: Text(
              invitation.invitedUserName[0].toUpperCase(),
              style: const TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.orange,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invitation.invitedUserName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'Invited ${_formatDate(invitation.createdAt)}',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.orange.withOpacity(0.2),
            ),
            child: Text(
              'Pending',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.orange,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPerformerCard(TeamMember member) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: const Color(0xffDA22FF).withOpacity(0.1),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xffDA22FF).withOpacity(0.2),
            child: Text(
              member.name[0].toUpperCase(),
              style: const TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Color(0xffDA22FF),
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.name,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${member.stats.goals} goals • ${member.stats.assists} assists',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${member.stats.rating}',
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Color(0xffDA22FF),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'captain':
        return const Color(0xFFFFD700); // Gold color
      case 'vice_captain':
        return Colors.orange;
      default:
        return const Color(0xffDA22FF);
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'captain':
        return 'Captain';
      case 'vice_captain':
        return 'Vice Captain';
      default:
        return 'Member';
    }
  }
}
