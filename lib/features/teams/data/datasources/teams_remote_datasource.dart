import 'dart:io';
import 'package:dio/dio.dart';
import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../dto/team_dto.dart';
import '../dto/team_request_dto.dart';

abstract class TeamsRemoteDataSource {
  Future<List<Team>> getMyTeams();
  Future<Team> getTeamById(String teamId);
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
  });
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
  });
  Future<void> deleteTeam(String teamId);
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  });
  Future<void> acceptInvitation(String invitationId);
  Future<void> declineInvitation(String invitationId);
  Future<void> removeMember({required String teamId, required String memberId});
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });
  Future<List<TeamInvitation>> getPendingInvitations();
  Future<String> uploadTeamLogo(File imageFile);
}

class TeamsRemoteDataSourceImpl implements TeamsRemoteDataSource {
  final ApiClient _apiClient;

  TeamsRemoteDataSourceImpl(this._apiClient);

  @override
  Future<List<Team>> getMyTeams() async {
    try {
      final response = await _apiClient.get(ApiConst.myTeamsEndpoint);

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) {
          final dto = TeamDto.fromJson(json);
          return _mapDtoToEntity(dto);
        }).toList();
      } else {
        throw Exception('Failed to load teams: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to load teams: $e');
    }
  }

  @override
  Future<Team> getTeamById(String teamId) async {
    try {
      final endpoint =
          ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
      final response = await _apiClient.get(endpoint);

      if (response.statusCode == 200) {
        final dto = TeamDto.fromJson(response.data);
        return _mapDtoToEntity(dto);
      } else {
        throw Exception('Failed to load team: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to load team: $e');
    }
  }

  @override
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
  }) async {
    try {
      final requestDto = CreateTeamRequestDto(
        name: name,
        description: description,
        logo: logo,
      );

      final response = await _apiClient.post(ApiConst.teamsEndpoint,
          data: requestDto.toJson());

      if (response.statusCode == 201 || response.statusCode == 200) {
        final dto = TeamDto.fromJson(response.data);
        return _mapDtoToEntity(dto);
      } else {
        throw Exception('Failed to create team: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to create team: $e');
    }
  }

  @override
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
  }) async {
    try {
      final endpoint =
          ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
      final requestDto = UpdateTeamRequestDto(
        name: name,
        description: description,
        logo: logo,
      );

      final response =
          await _apiClient.put(endpoint, data: requestDto.toJson());

      if (response.statusCode == 200) {
        final dto = TeamDto.fromJson(response.data);
        return _mapDtoToEntity(dto);
      } else {
        throw Exception('Failed to update team: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to update team: $e');
    }
  }

  @override
  Future<void> deleteTeam(String teamId) async {
    try {
      final endpoint =
          ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
      final response = await _apiClient.delete(endpoint);

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete team: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to delete team: $e');
    }
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    try {
      final endpoint =
          ApiConst.teamInvitePlayerEndpoint.replaceAll('{teamId}', teamId);
      final requestDto = InvitePlayerRequestDto(
        playerId: playerId,
        message: message,
      );

      final response =
          await _apiClient.post(endpoint, data: requestDto.toJson());

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Failed to invite player: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to invite player: $e');
    }
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    try {
      final endpoint = ApiConst.teamInvitationResponseEndpoint
          .replaceAll('{invitationId}', invitationId);
      final requestDto = InvitationResponseRequestDto(action: 'accept');

      final response =
          await _apiClient.post(endpoint, data: requestDto.toJson());

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to accept invitation: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to accept invitation: $e');
    }
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    try {
      final endpoint = ApiConst.teamInvitationResponseEndpoint
          .replaceAll('{invitationId}', invitationId);
      final requestDto = InvitationResponseRequestDto(action: 'decline');

      final response =
          await _apiClient.post(endpoint, data: requestDto.toJson());

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to decline invitation: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to decline invitation: $e');
    }
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    try {
      final endpoint = ApiConst.teamMembersEndpoint
          .replaceAll('{teamId}', teamId)
          .replaceAll('{memberId}', memberId);

      final response = await _apiClient.delete(endpoint);

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to remove member: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to remove member: $e');
    }
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    try {
      final endpoint = ApiConst.teamMemberRoleEndpoint
          .replaceAll('{teamId}', teamId)
          .replaceAll('{memberId}', memberId);

      final requestDto = UpdateMemberRoleRequestDto(role: role);
      final response =
          await _apiClient.put(endpoint, data: requestDto.toJson());

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to update member role: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to update member role: $e');
    }
  }

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    try {
      final response = await _apiClient.get(ApiConst.teamInvitationsEndpoint);

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? response.data;
        return data.map((json) {
          final dto = TeamInvitationDto.fromJson(json);
          return _mapInvitationDtoToEntity(dto);
        }).toList();
      } else {
        throw Exception(
            'Failed to load invitations: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to load invitations: $e');
    }
  }

  // Helper methods to map DTOs to entities
  Team _mapDtoToEntity(TeamDto dto) {
    return Team(
      id: dto.id,
      name: dto.name,
      description: dto.description,
      logo: dto.logo,
      createdBy: dto.createdBy,
      createdAt: DateTime.parse(dto.createdAt),
      updatedAt: DateTime.parse(dto.updatedAt),
      isActive: dto.isActive,
      members:
          dto.members.map((member) => _mapMemberDtoToEntity(member)).toList(),
      invitations: dto.invitations
          .map((invitation) => _mapInvitationDtoToEntity(invitation))
          .toList(),
      stats: _mapStatsDtoToEntity(dto.stats),
    );
  }

  TeamMember _mapMemberDtoToEntity(TeamMemberDto dto) {
    return TeamMember(
      id: dto.id,
      userId: dto.userId,
      name: dto.name,
      profileImage: dto.profileImage,
      position: dto.position,
      role: dto.role,
      joinedAt: DateTime.parse(dto.joinedAt),
      stats: _mapPlayerStatsDtoToEntity(dto.stats),
      isActive: dto.isActive,
    );
  }

  TeamInvitation _mapInvitationDtoToEntity(TeamInvitationDto dto) {
    return TeamInvitation(
      id: dto.id,
      teamId: dto.teamId,
      invitedUserId: dto.invitedUserId,
      invitedUserName: dto.invitedUserName,
      invitedUserEmail: dto.invitedUserEmail,
      invitedUserPhone: dto.invitedUserPhone,
      status: dto.status,
      createdAt: DateTime.parse(dto.createdAt),
      respondedAt:
          dto.respondedAt != null ? DateTime.parse(dto.respondedAt!) : null,
    );
  }

  TeamStats _mapStatsDtoToEntity(TeamStatsDto dto) {
    return TeamStats(
      totalMatches: dto.totalMatches,
      wins: dto.wins,
      losses: dto.losses,
      draws: dto.draws,
      winRate: dto.winRate,
      totalGoals: dto.totalGoals,
      goalsConceded: dto.goalsConceded,
      cleanSheets: dto.cleanSheets,
      averageGoalsPerMatch: dto.averageGoalsPerMatch,
    );
  }

  PlayerStats _mapPlayerStatsDtoToEntity(PlayerStatsDto dto) {
    return PlayerStats(
      matchesPlayed: dto.matchesPlayed,
      goals: dto.goals,
      assists: dto.assists,
      cleanSheets: dto.cleanSheets,
      rating: dto.rating,
      yellowCards: dto.yellowCards,
      redCards: dto.redCards,
      minutesPlayed: dto.minutesPlayed,
    );
  }

  @override
  Future<String> uploadTeamLogo(File imageFile) async {
    try {
      // Create FormData for file upload
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'team_logo_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ),
        'type': 'team_logo',
      });

      final response = await _apiClient.post(
        ApiConst.fileUploadEndpoint,
        data: formData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data['url'] ?? response.data['file_url'] ?? '';
      } else {
        throw Exception('Failed to upload logo: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Failed to upload logo: $e');
    }
  }
}
