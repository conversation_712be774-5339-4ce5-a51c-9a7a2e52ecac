import '../../domain/entities/team.dart';
import '../../domain/repositories/teams_repository.dart';
import '../datasources/teams_remote_datasource.dart';
import '../datasources/teams_local_datasource.dart';

class TeamsRepositoryImpl implements TeamsRepository {
  final TeamsRemoteDataSource _remoteDataSource;
  final TeamsLocalDataSource _localDataSource;

  TeamsRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<Team>> getMyTeams() async {
    try {
      // Try to get from remote first
      final teams = await _remoteDataSource.getMyTeams();

      // Cache the teams locally
      await _localDataSource.saveTeams(teams);

      return teams;
    } catch (e) {
      // If remote fails, try to get from local cache
      try {
        return await _localDataSource.getMyTeams();
      } catch (cacheError) {
        throw Exception('Failed to load teams: $e');
      }
    }
  }

  @override
  Future<Team> getTeamById(String teamId) async {
    try {
      // Try to get from remote first
      final team = await _remoteDataSource.getTeamById(teamId);

      // Cache the team locally
      await _localDataSource.saveTeam(team);

      return team;
    } catch (e) {
      // If remote fails, try to get from local cache
      try {
        final cachedTeam = await _localDataSource.getTeamById(teamId);
        if (cachedTeam != null) {
          return cachedTeam;
        }
        throw Exception('Team not found');
      } catch (cacheError) {
        throw Exception('Failed to load team: $e');
      }
    }
  }

  @override
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
  }) async {
    try {
      final team = await _remoteDataSource.createTeam(
        name: name,
        description: description,
        logo: logo,
      );

      // Cache the new team locally
      await _localDataSource.saveTeam(team);

      return team;
    } catch (e) {
      throw Exception('Failed to create team: $e');
    }
  }

  @override
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
  }) async {
    try {
      final team = await _remoteDataSource.updateTeam(
        teamId: teamId,
        name: name,
        description: description,
        logo: logo,
      );

      // Update the team in local cache
      await _localDataSource.saveTeam(team);

      return team;
    } catch (e) {
      throw Exception('Failed to update team: $e');
    }
  }

  @override
  Future<void> deleteTeam(String teamId) async {
    try {
      await _remoteDataSource.deleteTeam(teamId);

      // Remove from local cache
      await _localDataSource.deleteTeam(teamId);
    } catch (e) {
      throw Exception('Failed to delete team: $e');
    }
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    try {
      await _remoteDataSource.invitePlayer(
        teamId: teamId,
        playerId: playerId,
        message: message,
      );
    } catch (e) {
      throw Exception('Failed to invite player: $e');
    }
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    try {
      await _remoteDataSource.acceptInvitation(invitationId);

      // Remove invitation from local cache
      await _localDataSource.deleteInvitation(invitationId);
    } catch (e) {
      throw Exception('Failed to accept invitation: $e');
    }
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    try {
      await _remoteDataSource.declineInvitation(invitationId);

      // Remove invitation from local cache
      await _localDataSource.deleteInvitation(invitationId);
    } catch (e) {
      throw Exception('Failed to decline invitation: $e');
    }
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    try {
      await _remoteDataSource.removeMember(
        teamId: teamId,
        memberId: memberId,
      );

      // Update the team in local cache
      final updatedTeam = await _remoteDataSource.getTeamById(teamId);
      await _localDataSource.saveTeam(updatedTeam);
    } catch (e) {
      throw Exception('Failed to remove member: $e');
    }
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    try {
      await _remoteDataSource.updateMemberRole(
        teamId: teamId,
        memberId: memberId,
        role: role,
      );

      // Update the team in local cache
      final updatedTeam = await _remoteDataSource.getTeamById(teamId);
      await _localDataSource.saveTeam(updatedTeam);
    } catch (e) {
      throw Exception('Failed to update member role: $e');
    }
  }

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    try {
      // Try to get from remote first
      final invitations = await _remoteDataSource.getPendingInvitations();

      // Cache the invitations locally
      for (final invitation in invitations) {
        await _localDataSource.saveInvitation(invitation);
      }

      return invitations;
    } catch (e) {
      // If remote fails, try to get from local cache
      try {
        return await _localDataSource.getPendingInvitations();
      } catch (cacheError) {
        throw Exception('Failed to load invitations: $e');
      }
    }
  }
}
