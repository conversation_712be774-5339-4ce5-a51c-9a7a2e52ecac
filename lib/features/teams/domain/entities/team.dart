class Team {
  final String id;
  final String name;
  final String description;
  final String? logo;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final List<TeamMember> members;
  final List<TeamInvitation> invitations;
  final TeamStats stats;

  const Team({
    required this.id,
    required this.name,
    required this.description,
    this.logo,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    required this.isActive,
    required this.members,
    required this.invitations,
    required this.stats,
  });

  factory Team.fromJson(Map<String, dynamic> json) {
    return Team(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      logo: json['logo'] as String?,
      createdBy: json['created_by'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool,
      members:
          (json['members'] as List<dynamic>)
              .map((member) => TeamMember.fromJson(member))
              .toList(),
      invitations:
          (json['invitations'] as List<dynamic>)
              .map((invitation) => TeamInvitation.fromJson(invitation))
              .toList(),
      stats: TeamStats.fromJson(json['stats']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo': logo,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'members': members.map((member) => member.toJson()).toList(),
      'invitations':
          invitations.map((invitation) => invitation.toJson()).toList(),
      'stats': stats.toJson(),
    };
  }

  Team copyWith({
    String? id,
    String? name,
    String? description,
    String? logo,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    List<TeamMember>? members,
    List<TeamInvitation>? invitations,
    TeamStats? stats,
  }) {
    return Team(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logo: logo ?? this.logo,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      members: members ?? this.members,
      invitations: invitations ?? this.invitations,
      stats: stats ?? this.stats,
    );
  }
}

class TeamMember {
  final String id;
  final String userId;
  final String name;
  final String? profileImage;
  final String position;
  final String role; // 'captain', 'vice_captain', 'member'
  final DateTime joinedAt;
  final PlayerStats stats;
  final bool isActive;

  const TeamMember({
    required this.id,
    required this.userId,
    required this.name,
    this.profileImage,
    required this.position,
    required this.role,
    required this.joinedAt,
    required this.stats,
    required this.isActive,
  });

  factory TeamMember.fromJson(Map<String, dynamic> json) {
    return TeamMember(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      name: json['name'] as String,
      profileImage: json['profile_image'] as String?,
      position: json['position'] as String,
      role: json['role'] as String,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      stats: PlayerStats.fromJson(json['stats']),
      isActive: json['is_active'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'profile_image': profileImage,
      'position': position,
      'role': role,
      'joined_at': joinedAt.toIso8601String(),
      'stats': stats.toJson(),
      'is_active': isActive,
    };
  }
}

class TeamInvitation {
  final String id;
  final String teamId;
  final String invitedUserId;
  final String invitedUserName;
  final String? invitedUserEmail;
  final String? invitedUserPhone;
  final String status; // 'pending', 'accepted', 'declined'
  final DateTime createdAt;
  final DateTime? respondedAt;

  const TeamInvitation({
    required this.id,
    required this.teamId,
    required this.invitedUserId,
    required this.invitedUserName,
    this.invitedUserEmail,
    this.invitedUserPhone,
    required this.status,
    required this.createdAt,
    this.respondedAt,
  });

  factory TeamInvitation.fromJson(Map<String, dynamic> json) {
    return TeamInvitation(
      id: json['id'] as String,
      teamId: json['team_id'] as String,
      invitedUserId: json['invited_user_id'] as String,
      invitedUserName: json['invited_user_name'] as String,
      invitedUserEmail: json['invited_user_email'] as String?,
      invitedUserPhone: json['invited_user_phone'] as String?,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      respondedAt:
          json['responded_at'] != null
              ? DateTime.parse(json['responded_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'team_id': teamId,
      'invited_user_id': invitedUserId,
      'invited_user_name': invitedUserName,
      'invited_user_email': invitedUserEmail,
      'invited_user_phone': invitedUserPhone,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'responded_at': respondedAt?.toIso8601String(),
    };
  }
}

class TeamStats {
  final int totalMatches;
  final int wins;
  final int losses;
  final int draws;
  final double winRate;
  final int totalGoals;
  final int goalsConceded;
  final int cleanSheets;
  final double averageGoalsPerMatch;

  const TeamStats({
    required this.totalMatches,
    required this.wins,
    required this.losses,
    required this.draws,
    required this.winRate,
    required this.totalGoals,
    required this.goalsConceded,
    required this.cleanSheets,
    required this.averageGoalsPerMatch,
  });

  factory TeamStats.fromJson(Map<String, dynamic> json) {
    return TeamStats(
      totalMatches: json['total_matches'] as int,
      wins: json['wins'] as int,
      losses: json['losses'] as int,
      draws: json['draws'] as int,
      winRate: (json['win_rate'] as num).toDouble(),
      totalGoals: json['total_goals'] as int,
      goalsConceded: json['goals_conceded'] as int,
      cleanSheets: json['clean_sheets'] as int,
      averageGoalsPerMatch: (json['average_goals_per_match'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_matches': totalMatches,
      'wins': wins,
      'losses': losses,
      'draws': draws,
      'win_rate': winRate,
      'total_goals': totalGoals,
      'goals_conceded': goalsConceded,
      'clean_sheets': cleanSheets,
      'average_goals_per_match': averageGoalsPerMatch,
    };
  }
}

class PlayerStats {
  final int matchesPlayed;
  final int goals;
  final int assists;
  final int cleanSheets;
  final double rating;
  final int yellowCards;
  final int redCards;
  final int minutesPlayed;

  const PlayerStats({
    required this.matchesPlayed,
    required this.goals,
    required this.assists,
    required this.cleanSheets,
    required this.rating,
    required this.yellowCards,
    required this.redCards,
    required this.minutesPlayed,
  });

  factory PlayerStats.fromJson(Map<String, dynamic> json) {
    return PlayerStats(
      matchesPlayed: json['matches_played'] as int,
      goals: json['goals'] as int,
      assists: json['assists'] as int,
      cleanSheets: json['clean_sheets'] as int,
      rating: (json['rating'] as num).toDouble(),
      yellowCards: json['yellow_cards'] as int,
      redCards: json['red_cards'] as int,
      minutesPlayed: json['minutes_played'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'matches_played': matchesPlayed,
      'goals': goals,
      'assists': assists,
      'clean_sheets': cleanSheets,
      'rating': rating,
      'yellow_cards': yellowCards,
      'red_cards': redCards,
      'minutes_played': minutesPlayed,
    };
  }
}
