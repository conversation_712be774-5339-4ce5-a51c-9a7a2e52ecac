import '../entities/team.dart';
import '../repositories/teams_repository.dart';

class GetMyTeamsUseCase {
  final TeamsRepository repository;

  GetMyTeamsUseCase(this.repository);

  Future<List<Team>> call() async {
    return await repository.getMyTeams();
  }
}

class GetTeamByIdUseCase {
  final TeamsRepository repository;

  GetTeamByIdUseCase(this.repository);

  Future<Team> call(String teamId) async {
    return await repository.getTeamById(teamId);
  }
}

class CreateTeamUseCase {
  final TeamsRepository repository;

  CreateTeamUseCase(this.repository);

  Future<Team> call({
    required String name,
    required String description,
    String? logo,
  }) async {
    return await repository.createTeam(
      name: name,
      description: description,
      logo: logo,
    );
  }
}

class UpdateTeamUseCase {
  final TeamsRepository repository;

  UpdateTeamUseCase(this.repository);

  Future<Team> call({
    required String teamId,
    String? name,
    String? description,
    String? logo,
  }) async {
    return await repository.updateTeam(
      teamId: teamId,
      name: name,
      description: description,
      logo: logo,
    );
  }
}

class DeleteTeamUseCase {
  final TeamsRepository repository;

  DeleteTeamUseCase(this.repository);

  Future<void> call(String teamId) async {
    return await repository.deleteTeam(teamId);
  }
}

class InvitePlayerUseCase {
  final TeamsRepository repository;

  InvitePlayerUseCase(this.repository);

  Future<void> call({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    return await repository.invitePlayer(
      teamId: teamId,
      playerId: playerId,
      message: message,
    );
  }
}

class AcceptInvitationUseCase {
  final TeamsRepository repository;

  AcceptInvitationUseCase(this.repository);

  Future<void> call(String invitationId) async {
    return await repository.acceptInvitation(invitationId);
  }
}

class DeclineInvitationUseCase {
  final TeamsRepository repository;

  DeclineInvitationUseCase(this.repository);

  Future<void> call(String invitationId) async {
    return await repository.declineInvitation(invitationId);
  }
}

class RemoveMemberUseCase {
  final TeamsRepository repository;

  RemoveMemberUseCase(this.repository);

  Future<void> call({required String teamId, required String memberId}) async {
    return await repository.removeMember(teamId: teamId, memberId: memberId);
  }
}

class UpdateMemberRoleUseCase {
  final TeamsRepository repository;

  UpdateMemberRoleUseCase(this.repository);

  Future<void> call({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    return await repository.updateMemberRole(
      teamId: teamId,
      memberId: memberId,
      role: role,
    );
  }
}

class GetPendingInvitationsUseCase {
  final TeamsRepository repository;

  GetPendingInvitationsUseCase(this.repository);

  Future<List<TeamInvitation>> call() async {
    return await repository.getPendingInvitations();
  }
}
