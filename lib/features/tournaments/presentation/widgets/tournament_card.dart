import 'package:flutter/material.dart';
import '../../domain/entities/tournament.dart';
import 'tournament_detail_bottom_sheet.dart';

class TournamentCard extends StatelessWidget {
  final Tournament tournament;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteTap;
  final bool isFavorite;
  final bool showFavoriteButton;

  const TournamentCard({
    super.key,
    required this.tournament,
    this.onTap,
    this.onFavoriteTap,
    this.isFavorite = false,
    this.showFavoriteButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showTournamentDetails(context),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tournament image
            Container(
              height: 160,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
                image: DecorationImage(
                  image: NetworkImage(tournament.bannerImage),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // Status badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor().withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tournament.statusDisplayText,
                        style: const TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  // Featured badge - removed as isFeatured property doesn't exist
                ],
              ),
            ),

            // Tournament info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and format
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          tournament.name,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xffDA22FF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xffDA22FF)),
                        ),
                        child: Text(
                          _formatDisplay(),
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Color(0xffDA22FF),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Organizer
                  Row(
                    children: [
                      const Icon(Icons.verified_user,
                          color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          tournament.organizerName,
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),

                  // Description
                  Text(
                    tournament.description,
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Location and dates
                  Row(
                    children: [
                      const Icon(Icons.location_on,
                          color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          _locationDisplay(),
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today,
                          color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${_formatDate(tournament.startDate)} - ${_formatDate(tournament.endDate)}',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Prize and entry fee
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          'Prize Pool',
                          _formatMoney(tournament.currency,
                              tournament.prize.totalAmount),
                          Icons.emoji_events,
                          const Color(0xffDA22FF),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildInfoChip(
                          'Entry Fee',
                          _formatEntryFee(
                              tournament.currency, tournament.entryFee),
                          Icons.payment,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Slots left
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          'Availability',
                          tournament.hasAvailableSlots
                              ? '${tournament.availableSlots} slots left'
                              : 'Full',
                          Icons.event_available,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildInfoChip(
                          'Age Group',
                          'All Ages', // Default since eligibility not in entity
                          Icons.person,
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Tags
                  if (tournament.tags.isNotEmpty)
                    Wrap(
                      spacing: 6,
                      runSpacing: 6,
                      children: tournament.tags
                          .take(3)
                          .map(
                            (tag) => Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  fontFamily: 'Gilroy_Medium',
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),

                  const SizedBox(height: 16),

                  // Action button (Join / Full / Closed)
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _joinButtonEnabled
                              ? () {
                                  if (onTap != null) {
                                    onTap!();
                                  } else {
                                    _showTournamentDetails(context);
                                  }
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xffDA22FF),
                            disabledBackgroundColor: Colors.grey,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(
                            _joinButtonText,
                            style: const TextStyle(
                              fontFamily: 'Gilroy_Bold',
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (tournament.status) {
      case TournamentStatus.registrationOpen:
        return Colors.green;
      case TournamentStatus.registrationClosed:
        return Colors.orange;
      case TournamentStatus.inProgress:
        return Colors.blue;
      case TournamentStatus.completed:
        return Colors.grey;
      case TournamentStatus.cancelled:
        return Colors.red;
      case TournamentStatus.draft:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final isSameDay =
        now.year == date.year && now.month == date.month && now.day == date.day;
    if (isSameDay) return 'Today';
    return '${date.day}/${date.month}/${date.year}';
  }

  String get _joinButtonText {
    if (tournament.status == TournamentStatus.registrationOpen) {
      return tournament.hasAvailableSlots ? 'Join' : 'Full';
    }
    if (tournament.status == TournamentStatus.registrationClosed) {
      return 'Closed';
    }
    // Default for other statuses
    return 'Closed';
  }

  bool get _joinButtonEnabled {
    return tournament.status == TournamentStatus.registrationOpen &&
        tournament.hasAvailableSlots;
  }

  String _formatMoney(String currency, double amount) {
    if (amount % 1 == 0) {
      return '$currency ${amount.toInt()}';
    }
    return '$currency $amount';
  }

  String _formatEntryFee(String currency, double amount) {
    if (amount == 0) return 'Free';
    return _formatMoney(currency, amount);
  }

  void _showTournamentDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TournamentDetailBottomSheet(tournament: tournament),
    );
  }

  String _formatDisplay() {
    return tournament.format.displayName;
  }

  String _locationDisplay() {
    return tournament.location;
  }
}

class TournamentCardShimmer extends StatelessWidget {
  const TournamentCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.05)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image placeholder
          Container(
            height: 160,
            width: double.infinity,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              color: Colors.black12,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 16, width: 180, color: Colors.black12),
                const SizedBox(height: 8),
                Container(height: 14, width: 240, color: Colors.black12),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Container(height: 36, color: Colors.black12),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
