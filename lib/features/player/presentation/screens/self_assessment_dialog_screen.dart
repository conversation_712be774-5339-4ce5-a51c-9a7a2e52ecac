import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/theme.dart';
import '../../../../utils/color.dart';
import '../../player_providers.dart';
import '../../domain/entities/player_rating.dart';
import '../../data/dto/player_request_models.dart';

class SelfAssessmentDialogScreen extends ConsumerStatefulWidget {
  final String playerId;

  const SelfAssessmentDialogScreen({
    super.key,
    required this.playerId,
  });

  @override
  ConsumerState<SelfAssessmentDialogScreen> createState() =>
      _SelfAssessmentDialogScreenState();
}

class _SelfAssessmentDialogScreenState
    extends ConsumerState<SelfAssessmentDialogScreen> {
  final Map<String, double> _ratings = {
    'defense': 50.0,
    'shooting': 50.0,
    'passing': 50.0,
    'pace': 50.0,
    'physicality': 50.0,
    'dribbling': 50.0,
  };

  final TextEditingController _commentsController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final playerRatingAsync = ref.watch(playerRatingProvider(widget.playerId));

    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        title: const Text(
          'Self Assessment',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            fontSize: 18,
            color: Colors.white,
          ),
        ),
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (!_isSubmitting)
            TextButton(
              onPressed: _submitAssessment,
              child: const Text(
                'Submit',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: const Color(0xffDA22FF),
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: NextSportzTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  _buildHeader(),

                  const SizedBox(height: 24),

                  // Current Rating Summary (if exists)
                  playerRatingAsync.when(
                    data: (result) => result.fold(
                      (error) => const SizedBox.shrink(),
                      (rating) => rating.totalRatings > 0
                          ? _buildCurrentRatingCard(rating)
                          : const SizedBox.shrink(),
                    ),
                    loading: () => const SizedBox.shrink(),
                    error: (error, stack) => const SizedBox.shrink(),
                  ),

                  if (playerRatingAsync.value?.fold((error) => false,
                          (rating) => rating.totalRatings > 0) ??
                      false)
                    const SizedBox(height: 24),

                  // Self Assessment Form
                  _buildSelfAssessmentForm(),

                  const SizedBox(height: 24),

                  // Assessment History (if exists)
                  playerRatingAsync.when(
                    data: (result) => result.fold(
                      (error) => const SizedBox.shrink(),
                      (rating) => rating.recentVotes.isNotEmpty
                          ? _buildAssessmentHistory(rating)
                          : const SizedBox.shrink(),
                    ),
                    loading: () => const SizedBox.shrink(),
                    error: (error, stack) => const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xffDA22FF).withOpacity(0.1),
            lightblue.withOpacity(0.3),
          ],
        ),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xffDA22FF),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.assessment,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 20),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Self Assessment',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    fontSize: 24,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Rate your performance across 6 key attributes to get your personalized rating',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    fontSize: 14,
                    color: Colors.grey,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentRatingCard(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: lightblue.withOpacity(0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 20),
              const SizedBox(width: 10),
              const Text(
                'Current Rating',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                (rating.averageRating / 10).toStringAsFixed(1),
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 28,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 6),
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '/10',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  Text(
                    'Overall',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${rating.totalRatings}',
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Bold',
                      fontSize: 18,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    'assessments',
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      fontSize: 11,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelfAssessmentForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: lightblue.withOpacity(0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rate Your Attributes',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 6),
          const Text(
            'Slide to rate each attribute from 1-100:',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              fontSize: 13,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ..._ratings.entries.map((entry) => _buildRatingSlider(entry)),
          const SizedBox(height: 16),
          TextField(
            controller: _commentsController,
            maxLines: 2,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Gilroy_Medium',
              fontSize: 14,
            ),
            decoration: InputDecoration(
              labelText: 'Comments (Optional)',
              labelStyle: TextStyle(
                color: grey,
                fontFamily: 'Gilroy_Medium',
                fontSize: 13,
              ),
              hintText: 'Add any comments about your assessment...',
              hintStyle: const TextStyle(
                color: Colors.grey,
                fontFamily: 'Gilroy_Medium',
                fontSize: 13,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: lightblue),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: lightblue),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: Color(0xffDA22FF), width: 2),
              ),
              filled: true,
              fillColor: mediumblue,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              gradient: NextSportzTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _submitAssessment,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Submit Assessment',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSlider(MapEntry<String, double> entry) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatCategoryName(entry.key),
                style: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getRatingColor(entry.value).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getRatingColor(entry.value).withOpacity(0.5),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${entry.value.round()}',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    fontSize: 13,
                    color: _getRatingColor(entry.value),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: _getRatingColor(entry.value),
              inactiveTrackColor: Colors.white.withOpacity(0.3),
              thumbColor: _getRatingColor(entry.value),
              overlayColor: _getRatingColor(entry.value).withOpacity(0.2),
              trackHeight: 4,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            ),
            child: Slider(
              value: entry.value,
              min: 1,
              max: 100,
              divisions: 99,
              onChanged: (value) {
                setState(() {
                  _ratings[entry.key] = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssessmentHistory(PlayerRating rating) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Assessments',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          ...rating.recentVotes.take(3).map((vote) => _buildHistoryItem(vote)),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(PlayerRatingVote vote) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${vote.averageRating.toStringAsFixed(1)}★',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  vote.voterName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                if (vote.comments != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    vote.comments!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          Text(
            _formatDate(vote.createdAt),
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCategoryName(String category) {
    return category.substring(0, 1).toUpperCase() + category.substring(1);
  }

  Color _getRatingColor(double rating) {
    if (rating >= 80) return Colors.green;
    if (rating >= 60) return Colors.orange;
    if (rating >= 40) return Colors.yellow[700]!;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  Future<void> _submitAssessment() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      final useCase = ref.read(submitPlayerRatingUseCaseProvider);
      await useCase(
        playerId: widget.playerId,
        ratingCategory: RatingCategory.self,
        defense: _ratings['defense']!.round(),
        shooting: _ratings['shooting']!.round(),
        passing: _ratings['passing']!.round(),
        pace: _ratings['pace']!.round(),
        physicality: _ratings['physicality']!.round(),
        dribbling: _ratings['dribbling']!.round(),
        comments: _commentsController.text.isNotEmpty
            ? _commentsController.text
            : null,
      );

      // Refresh data
      ref.invalidate(playerRatingProvider(widget.playerId));
      ref.invalidate(currentPlayerRatingProvider);

      // Reset form
      setState(() {
        _ratings.updateAll((key, value) => 50.0);
        _commentsController.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Assessment submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Close the dialog after successful submission
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit assessment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}
