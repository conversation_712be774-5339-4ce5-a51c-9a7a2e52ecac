import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../player_providers.dart';
import '../../domain/entities/player_rating.dart';
import '../../data/dto/player_request_models.dart';

class SelfAssessmentWidget extends ConsumerStatefulWidget {
  final String playerId;

  const SelfAssessmentWidget({
    super.key,
    required this.playerId,
  });

  @override
  ConsumerState<SelfAssessmentWidget> createState() =>
      _SelfAssessmentWidgetState();
}

class _SelfAssessmentWidgetState extends ConsumerState<SelfAssessmentWidget> {
  final Map<String, double> _ratings = {
    'defense': 50.0,
    'shooting': 50.0,
    'passing': 50.0,
    'pace': 50.0,
    'physicality': 50.0,
    'dribbling': 50.0,
  };

  final TextEditingController _commentsController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final playerRatingAsync = ref.watch(playerRatingProvider(widget.playerId));

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(playerRatingProvider(widget.playerId));
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current Rating Summary
              playerRatingAsync.when(
                data: (result) => result.fold(
                  (error) => _buildErrorWidget(error.message),
                  (rating) => _buildCurrentRatingCard(rating),
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, stack) => _buildErrorWidget(error.toString()),
              ),

              const SizedBox(height: 24),

              // Self Assessment Form
              _buildSelfAssessmentForm(),

              const SizedBox(height: 24),

              // Assessment History
              playerRatingAsync.when(
                data: (result) => result.fold(
                  (error) => const SizedBox.shrink(),
                  (rating) => _buildAssessmentHistory(rating),
                ),
                loading: () => const SizedBox.shrink(),
                error: (error, stack) => const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentRatingCard(PlayerRating rating) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber, size: 32),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Rating',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      rating.averageRating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${rating.totalRatings} total assessments',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCategoryBreakdown(rating.categoryAverages),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdown(Map<String, double> categoryAverages) {
    return Column(
      children: categoryAverages.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  _formatCategoryName(entry.key),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: entry.value / 100,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getRatingColor(entry.value),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${entry.value.toStringAsFixed(1)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSelfAssessmentForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Self Assessment',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Rate your current abilities in each category (1-100):',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ..._ratings.entries.map((entry) => _buildRatingSlider(entry)),
            const SizedBox(height: 16),
            TextField(
              controller: _commentsController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Comments (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Add any comments about your assessment...',
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitAssessment,
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Submit Assessment'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSlider(MapEntry<String, double> entry) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatCategoryName(entry.key),
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                '${entry.value.round()}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          Slider(
            value: entry.value,
            min: 1,
            max: 100,
            divisions: 99,
            activeColor: _getRatingColor(entry.value),
            onChanged: (value) {
              setState(() {
                _ratings[entry.key] = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAssessmentHistory(PlayerRating rating) {
    if (rating.recentVotes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Assessments',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...rating.recentVotes
                .take(5)
                .map((vote) => _buildHistoryItem(vote)),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(PlayerRatingVote vote) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${vote.averageRating.toStringAsFixed(1)}★',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  vote.voterName,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                if (vote.comments != null)
                  Text(
                    vote.comments!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          Text(
            _formatDate(vote.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Error: $message',
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCategoryName(String category) {
    return category.substring(0, 1).toUpperCase() + category.substring(1);
  }

  Color _getRatingColor(double rating) {
    if (rating >= 80) return Colors.green;
    if (rating >= 60) return Colors.orange;
    if (rating >= 40) return Colors.yellow[700]!;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inMinutes}m ago';
    }
  }

  Future<void> _submitAssessment() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      final useCase = ref.read(submitPlayerRatingUseCaseProvider);
      await useCase(
        playerId: widget.playerId,
        ratingCategory: RatingCategory.self,
        defense: _ratings['defense']!.round(),
        shooting: _ratings['shooting']!.round(),
        passing: _ratings['passing']!.round(),
        pace: _ratings['pace']!.round(),
        physicality: _ratings['physicality']!.round(),
        dribbling: _ratings['dribbling']!.round(),
        comments: _commentsController.text.isNotEmpty
            ? _commentsController.text
            : null,
      );

      // Refresh data
      ref.invalidate(playerRatingProvider(widget.playerId));

      // Reset form
      setState(() {
        _ratings.updateAll((key, value) => 50.0);
        _commentsController.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Assessment submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit assessment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}
