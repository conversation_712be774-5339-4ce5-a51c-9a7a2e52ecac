/// Player rating vote entity
class PlayerRatingVote {
  final String id;
  final String ratingCategory;
  final int defense;
  final int shooting;
  final int passing;
  final int pace;
  final int physicality;
  final int dribbling;
  final String? comments;
  final String voterName;
  final DateTime createdAt;

  const PlayerRatingVote({
    required this.id,
    required this.ratingCategory,
    required this.defense,
    required this.shooting,
    required this.passing,
    required this.pace,
    required this.physicality,
    required this.dribbling,
    this.comments,
    required this.voterName,
    required this.createdAt,
  });

  /// Calculate average rating from all attributes
  double get averageRating {
    final total = defense + shooting + passing + pace + physicality + dribbling;
    return total / 6.0;
  }

  PlayerRatingVote copyWith({
    String? id,
    String? ratingCategory,
    int? defense,
    int? shooting,
    int? passing,
    int? pace,
    int? physicality,
    int? dribbling,
    String? comments,
    String? voterName,
    DateTime? createdAt,
  }) {
    return PlayerRatingVote(
      id: id ?? this.id,
      ratingCategory: ratingCategory ?? this.ratingCategory,
      defense: defense ?? this.defense,
      shooting: shooting ?? this.shooting,
      passing: passing ?? this.passing,
      pace: pace ?? this.pace,
      physicality: physicality ?? this.physicality,
      dribbling: dribbling ?? this.dribbling,
      comments: comments ?? this.comments,
      voterName: voterName ?? this.voterName,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// Player rating entity (works with DTO format)
class PlayerRating {
  final double averageRating;
  final int totalRatings;
  final Map<String, double> categoryAverages;
  final List<PlayerRatingVote> recentVotes;

  const PlayerRating({
    required this.averageRating,
    required this.totalRatings,
    required this.categoryAverages,
    required this.recentVotes,
  });

  /// Check if there's a self-assessment in recent votes
  bool get hasSelfAssessment {
    return recentVotes
        .any((vote) => vote.ratingCategory.toLowerCase() == 'self');
  }

  /// Get the most recent self-assessment
  PlayerRatingVote? get latestSelfAssessment {
    final selfVotes = recentVotes
        .where((vote) => vote.ratingCategory.toLowerCase() == 'self')
        .toList();

    if (selfVotes.isEmpty) return null;

    selfVotes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return selfVotes.first;
  }

  /// Get category average for a specific attribute
  double getCategoryAverage(String category) {
    return categoryAverages[category] ?? 0.0;
  }

  /// Get defense rating
  double get defense => getCategoryAverage('defense');

  /// Get shooting rating
  double get shooting => getCategoryAverage('shooting');

  /// Get passing rating
  double get passing => getCategoryAverage('passing');

  /// Get pace rating
  double get pace => getCategoryAverage('pace');

  /// Get physicality rating
  double get physicality => getCategoryAverage('physicality');

  /// Get dribbling rating
  double get dribbling => getCategoryAverage('dribbling');

  PlayerRating copyWith({
    double? averageRating,
    int? totalRatings,
    Map<String, double>? categoryAverages,
    List<PlayerRatingVote>? recentVotes,
  }) {
    return PlayerRating(
      averageRating: averageRating ?? this.averageRating,
      totalRatings: totalRatings ?? this.totalRatings,
      categoryAverages: categoryAverages ?? this.categoryAverages,
      recentVotes: recentVotes ?? this.recentVotes,
    );
  }
}
