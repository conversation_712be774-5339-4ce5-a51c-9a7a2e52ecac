import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/venue_selector.dart';
import '../../../../utils/color.dart';

class VenueSelectionModal extends ConsumerStatefulWidget {
  final List<String> selectedVenueIds;
  final Function(List<String>) onVenuesChanged;
  final String? title;
  final String? hint;

  const VenueSelectionModal({
    Key? key,
    required this.selectedVenueIds,
    required this.onVenuesChanged,
    this.title,
    this.hint,
  }) : super(key: key);

  @override
  ConsumerState<VenueSelectionModal> createState() =>
      _VenueSelectionModalState();
}

class _VenueSelectionModalState extends ConsumerState<VenueSelectionModal>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  List<String> _tempSelectedVenues = [];

  @override
  void initState() {
    super.initState();
    _tempSelectedVenues = List.from(widget.selectedVenueIds);

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
        .animate(CurvedAnimation(
            parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onVenuesChanged(List<String> venueIds) {
    setState(() {
      _tempSelectedVenues = venueIds;
    });
  }

  void _confirmSelection() {
    widget.onVenuesChanged(_tempSelectedVenues);
    Navigator.of(context).pop();
  }

  void _cancelSelection() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: Column(
            children: [
              // Header
              _buildHeader(),

              // Divider
              Container(
                height: 1,
                color: Colors.white.withOpacity(0.1),
              ),

              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: VenueSelector(
                    selectedVenueIds: _tempSelectedVenues,
                    onVenuesChanged: _onVenuesChanged,
                    hint: widget.hint ?? 'Search and select venues...',
                    showNearbyVenues: true,
                  ),
                ),
              ),

              // Bottom actions
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Close button
          GestureDetector(
            onTap: _cancelSelection,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title ?? 'Select Venues',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontFamily: 'Gilroy_Bold',
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Choose one or more venues for your match',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                    fontFamily: 'Gilroy_Medium',
                  ),
                ),
              ],
            ),
          ),

          // Selected count
          if (_tempSelectedVenues.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xffDA22FF).withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xffDA22FF).withOpacity(0.3),
                ),
              ),
              child: Text(
                '${_tempSelectedVenues.length} selected',
                style: const TextStyle(
                  color: Color(0xffDA22FF),
                  fontSize: 12,
                  fontFamily: 'Gilroy_Medium',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: _cancelSelection,
              style: OutlinedButton.styleFrom(
                backgroundColor: Colors.transparent,
                side: BorderSide(
                  color: Colors.white.withOpacity(0.3),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 16,
                  fontFamily: 'Gilroy_Medium',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Confirm button
          Expanded(
            child: ElevatedButton(
              onPressed:
                  _tempSelectedVenues.isNotEmpty ? _confirmSelection : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _tempSelectedVenues.isNotEmpty
                    ? const Color(0xffDA22FF)
                    : Colors.white.withOpacity(0.1),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'Confirm (${_tempSelectedVenues.length})',
                style: TextStyle(
                  color: _tempSelectedVenues.isNotEmpty
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                  fontSize: 16,
                  fontFamily: 'Gilroy_Medium',
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to show venue selection modal
Future<List<String>> showVenueSelectionModal({
  required BuildContext context,
  required List<String> selectedVenueIds,
  String? title,
  String? hint,
}) async {
  List<String> result = List.from(selectedVenueIds);

  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => VenueSelectionModal(
      selectedVenueIds: selectedVenueIds,
      onVenuesChanged: (venues) {
        result = venues;
      },
      title: title,
      hint: hint,
    ),
  );

  return result;
}
