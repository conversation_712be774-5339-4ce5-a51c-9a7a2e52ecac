import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../../venues_providers.dart';
import '../widgets/venue_list.dart';

class VenuesScreen extends ConsumerStatefulWidget {
  const VenuesScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<VenuesScreen> createState() => _VenuesScreenState();
}

class _VenuesScreenState extends ConsumerState<VenuesScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Color(0xffDA22FF)),
                const SizedBox(width: 8),
                const Text(
                  'Venues',
                  style: TextStyle(
                    fontFamily: '<PERSON><PERSON>_Bold',
                    color: Colors.white,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildFilterChip(context),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Info bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                gradient: LinearGradient(
                  colors: [
                    const Color(0xffDA22FF).withOpacity(0.2),
                    lightblue.withOpacity(0.4),
                  ],
                ),
                border: Border.all(color: Colors.white.withOpacity(0.08)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.sports_soccer,
                    color: Colors.white70,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Discover and book amazing soccer venues near you!',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.85),
                        fontSize: 12,
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Venues list
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: VenueList(
                hint: 'Search venues...',
                showSearchBar: true,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.08)),
      ),
      child: Row(
        children: const [
          Icon(Icons.tune, color: Colors.white70, size: 16),
          SizedBox(width: 6),
          Text(
            'Filters',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }
}
