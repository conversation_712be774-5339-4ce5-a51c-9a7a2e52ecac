import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';

/// Player Challenge Card - Placeholder until backend ranking is implemented
class PlayerChallengeCard extends ConsumerWidget {
  final String playerId;
  final bool isExpanded;
  final VoidCallback? onTap;

  const PlayerChallengeCard({
    super.key,
    required this.playerId,
    this.isExpanded = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              lightblue.withOpacity(0.8),
              const Color(0xff1E3A8A).withOpacity(0.6),
              const Color(0xff9333EA).withOpacity(0.4),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: _buildPlaceholderContent(),
      ),
    );
  }

  Widget _buildPlaceholderContent() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.person, color: Colors.white70, size: 40),
          const SizedBox(height: 16),
          const Text(
            'Player Profile',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Player ID: $playerId',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              'Ranking system coming soon',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact player card - Placeholder until backend ranking is implemented
class CompactPlayerCard extends ConsumerWidget {
  final String playerId;
  final VoidCallback? onTap;

  const CompactPlayerCard({super.key, required this.playerId, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: lightblue.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: const Color(0xffDA22FF).withOpacity(0.3),
              child: const Icon(Icons.person, color: Color(0xffDA22FF)),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Player $playerId',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Gilroy_Bold',
                    ),
                  ),
                  const Text(
                    'Ranking coming soon',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 11,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
