import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../challenges_providers.dart';
import '../widgets/challenge_filters_widget.dart';
import '../../../../utils/color.dart';
import '../../data/dto/challenge_dto.dart';
import '../../domain/usecases/challenge_usecases.dart';
import '../../domain/entities/challenge_enums.dart';
import '../../../venues/presentation/widgets/venue_selection_modal.dart';
import '../../../venues/presentation/widgets/selected_venues_display.dart';

class ChallengesScreen extends ConsumerStatefulWidget {
  const ChallengesScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ChallengesScreen> createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends ConsumerState<ChallengesScreen> {
  @override
  void initState() {
    super.initState();
    // Load challenges with current filters (including default 'Nearby') when screen first loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentFilters = ref.read(challengeFiltersProvider);
      ref
          .read(challengeListStateProvider.notifier)
          .loadChallenges(filters: currentFilters, refresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            // Header
            Row(
              children: [
                const Icon(Icons.flash_on, color: Color(0xffDA22FF)),
                const SizedBox(width: 8),
                const Text(
                  'Challenges',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildStatsChip(context),
              ],
            ),
            const SizedBox(height: 16),
            // Creative "Create Challenge" card
            _buildCreateChallengeCard(context),
            const SizedBox(height: 8),
            // Fun tip bar
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                gradient: LinearGradient(
                  colors: [
                    const Color(0xffDA22FF).withOpacity(0.2),
                    lightblue.withOpacity(0.4),
                  ],
                ),
                border: Border.all(color: Colors.white.withOpacity(0.08)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.sports_soccer,
                    color: Colors.white70,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Post a challenge and let nearby teams accept! Level up your streaks and win wagers.',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.85),
                        fontSize: 12,
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            // Quick filters
            QuickFiltersWidget(onFilterTap: () => _showFiltersModal(context)),
            const SizedBox(height: 16),
            // Challenge list with state management
            _buildChallengeList(),
            const SizedBox(height: 20), // Extra space at bottom
          ],
        ),
      ),
    );
  }

  Widget _buildStatsChip(BuildContext context) {
    final challengeListState = ref.watch(challengeListStateProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.08)),
      ),
      child: Row(
        children: [
          const Icon(Icons.sports_soccer, color: Colors.white70, size: 16),
          const SizedBox(width: 6),
          Text(
            '${challengeListState.challenges.length} open',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateChallengeCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xffDA22FF).withOpacity(0.9),
            const Color(0xff9733EE).withOpacity(0.8),
            const Color(0xff00E676).withOpacity(0.7),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xffDA22FF).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.emoji_events,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Ready to Challenge?',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Create your own challenge and invite teams to compete!',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        fontFamily: 'Gilroy_Medium',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(flex: 2, child: _buildCreateButton(context)),
              const SizedBox(width: 12),
              Expanded(child: _buildQuickJoinButton(context)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCreateButton(BuildContext context) {
    return InkWell(
      onTap: () => _openCreateChallengeSheet(context),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.add_circle, color: Color(0xffDA22FF), size: 20),
            const SizedBox(width: 8),
            const Text(
              'Create Challenge',
              style: TextStyle(
                color: Color(0xffDA22FF),
                fontWeight: FontWeight.bold,
                fontSize: 14,
                fontFamily: 'Gilroy_Bold',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickJoinButton(BuildContext context) {
    return InkWell(
      onTap: () => _showSnack(context, 'Quick Join feature coming soon!'),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: const Icon(Icons.bolt, color: Colors.white, size: 20),
      ),
    );
  }

  void _openCreateChallengeSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.85,
          maxChildSize: 0.95,
          minChildSize: 0.6,
          builder: (_, controller) {
            return Container(
              decoration: BoxDecoration(
                color: lightblue,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: _SimpleCreateChallengeForm(scrollController: controller),
            );
          },
        );
      },
    );
  }

  void _showSnack(BuildContext context, String msg) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(msg), behavior: SnackBarBehavior.floating),
    );
  }

  Widget _buildChallengeList() {
    final challengeListState = ref.watch(challengeListStateProvider);

    // Load challenges when filters change
    ref.listen(challengeFiltersProvider, (previous, next) {
      if (previous != next) {
        Future.microtask(() {
          ref
              .read(challengeListStateProvider.notifier)
              .loadChallenges(filters: next, refresh: true);
        });
      }
    });

    if (challengeListState.isLoading && challengeListState.challenges.isEmpty) {
      return _buildLoadingList();
    }

    if (challengeListState.error != null &&
        challengeListState.challenges.isEmpty) {
      return _buildErrorState(challengeListState.error!);
    }

    if (challengeListState.challenges.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        ...challengeListState.challenges.asMap().entries.map((entry) {
          final index = entry.key;
          final challenge = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _EnhancedChallengeCard(challenge: challenge, index: index),
          );
        }).toList(),
        if (challengeListState.hasMore) _buildLoadMoreButton(),
      ],
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        3,
        (index) => Container(
          margin: const EdgeInsets.only(bottom: 16),
          height: 200,
          decoration: BoxDecoration(
            color: lightblue.withOpacity(0.3),
            borderRadius: BorderRadius.circular(18),
          ),
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xffDA22FF)),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(Icons.error_outline, color: Colors.red, size: 48),
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load challenges',
            style: TextStyle(
              color: Colors.red,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getUserFriendlyErrorMessage(error),
            style: TextStyle(
              color: Colors.red.withOpacity(0.8),
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildErrorStateAction(
                icon: Icons.refresh,
                label: 'Retry',
                onTap: () =>
                    ref.read(challengeListStateProvider.notifier).refresh(),
                color: Colors.red,
              ),
              _buildErrorStateAction(
                icon: Icons.filter_list,
                label: 'Adjust Filters',
                onTap: () => _showFiltersModal(context),
                color: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getUserFriendlyErrorMessage(String error) {
    if (error.contains("type 'Null' is not a subtype of type 'int'")) {
      return "There was an issue processing the server response.\nPlease try refreshing or contact support if the problem persists.";
    } else if (error.contains("NetworkException")) {
      return "Unable to connect to the server.\nPlease check your internet connection and try again.";
    } else if (error.contains("TimeoutException")) {
      return "The request took too long to complete.\nPlease try again or check your connection.";
    } else {
      return "An unexpected error occurred.\nPlease try refreshing the page.";
    }
  }

  Widget _buildErrorStateAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontFamily: 'Gilroy_Medium',
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xffDA22FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.sports_soccer,
              color: Color(0xffDA22FF),
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'No Challenges Available',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Gilroy_Bold',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to create a challenge in your area!\nTap the "Create Challenge" button above to get started.',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontFamily: 'Gilroy_Medium',
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildEmptyStateAction(
                icon: Icons.filter_list,
                label: 'Adjust Filters',
                onTap: () => _showFiltersModal(context),
              ),
              _buildEmptyStateAction(
                icon: Icons.refresh,
                label: 'Refresh',
                onTap: () =>
                    ref.read(challengeListStateProvider.notifier).refresh(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStateAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: const Color(0xffDA22FF), size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Gilroy_Medium',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: ElevatedButton(
        onPressed: () =>
            ref.read(challengeListStateProvider.notifier).loadChallenges(),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white.withOpacity(0.1),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text('Load More'),
      ),
    );
  }

  void _showFiltersModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        expand: false,
        initialChildSize: 0.85,
        maxChildSize: 0.95,
        minChildSize: 0.6,
        builder: (_, controller) => ChallengeFiltersWidget(
          onFiltersApplied: () {
            // Refresh challenges when filters are applied
            ref.read(challengeListStateProvider.notifier).refresh();
          },
        ),
      ),
    );
  }
}

// Enhanced challenge card that uses actual challenge data
class _EnhancedChallengeCard extends StatelessWidget {
  final dynamic challenge; // Will be Challenge entity in real implementation
  final int index;

  const _EnhancedChallengeCard({required this.challenge, required this.index});

  @override
  Widget build(BuildContext context) {
    // For now, we'll use the existing mock data structure
    // In real implementation, this would use the Challenge entity
    return _ChallengeCard(index: index);
  }
}

class _ChallengeCard extends StatelessWidget {
  final int index;
  const _ChallengeCard({required this.index});

  @override
  Widget build(BuildContext context) {
    final matchType = ["5v5", "7v7", "11v11"][index % 3];
    final timePref =
        ["Tonight 7-9 PM", "Sat Morning", "Sun Evening"][index % 3];
    final wager = ["Losers pay", "+ NPR 500", "+ NPR 1000"][index % 3];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: lightblue.withOpacity(0.5),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: Colors.white.withOpacity(0.12),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _badge(
                Icon(
                  Icons.local_fire_department,
                  color: Colors.white,
                  size: 14,
                ),
                'Streak x${(index % 4) + 1}',
              ),
              const SizedBox(width: 8),
              _badge(
                Icon(Icons.shield, color: Colors.white, size: 14),
                '${(index * 7) % 100} Elo',
              ), // Elo rating system - measures player/team skill level
              const Spacer(),
              Icon(
                Icons.more_horiz,
                color: Colors.white.withOpacity(0.8),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              CircleAvatar(
                radius: 22,
                backgroundColor: Colors.white.withOpacity(0.15),
                child: const Icon(Icons.group, color: Colors.white),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      [
                        'FC Barcelona',
                        'Real Madrid',
                        'Manchester United',
                        'Liverpool',
                        'Chelsea',
                        'Arsenal',
                      ][index % 6],
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Looking for Opponent',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.75),
                        fontSize: 13,
                        fontFamily: 'Gilroy_Medium',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    LayoutBuilder(
                      builder: (context, constraints) {
                        final double gap = 8;
                        final double tileW = (constraints.maxWidth - gap) / 2;
                        final items = <Widget>[
                          _infoTile(Icons.sports_soccer, matchType, tileW),
                          _infoTile(
                            Icons.place,
                            [
                              'Bhatbhateni Futsal',
                              'Dhumbarahi',
                              'Satdobato',
                            ][index % 3],
                            tileW,
                          ),
                          _infoTile(Icons.schedule, timePref, tileW),
                          _infoTile(Icons.attach_money, wager, tileW),
                        ];
                        return Wrap(
                          spacing: gap,
                          runSpacing: gap,
                          alignment: WrapAlignment.spaceBetween,
                          children: items,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _ctaButton(
                  context,
                  icon: Icons.sports_kabaddi,
                  label: 'Accept Challenge',
                  color: const Color(0xff00E676),
                  onTap: () => _showAcceptChallengeBottomSheet(context, index),
                ),
              ),
              const SizedBox(width: 10),
              _ctaButton(
                context,
                icon: Icons.share,
                label: 'Share',
                color: const Color(0xffDA22FF),
                onTap: () => _showSnack(context, 'Shared'),
                isSmall: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _badge(Widget icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.15)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: 14, height: 14, child: icon),
          const SizedBox(width: 6),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
              fontWeight: FontWeight.w600,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  Widget _infoTile(IconData icon, String text, double width) {
    return SizedBox(
      width: width,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          color: Colors.white.withOpacity(0.08),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Row(
          children: [
            Icon(icon, size: 18, color: Colors.white70),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                text,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontFamily: 'Gilroy_Medium',
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _ctaButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isSmall = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(14),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          gradient: LinearGradient(
            colors: [
              color.withOpacity(0.9),
              color.withOpacity(0.6),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.25),
              blurRadius: 10,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: isSmall ? 18 : 20),
            if (!isSmall) const SizedBox(width: 8),
            if (!isSmall)
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showSnack(BuildContext context, String msg) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(msg), behavior: SnackBarBehavior.floating),
    );
  }

  void _showAcceptChallengeBottomSheet(BuildContext context, int index) {
    final matchType = ["5v5", "7v7", "11v11"][index % 3];
    final timePref =
        ["Tonight 7-9 PM", "Sat Morning", "Sun Evening"][index % 3];
    final wager = ["Losers pay", "+ NPR 500", "+ NPR 1000"][index % 3];
    final teamName = [
      'FC Barcelona',
      'Real Madrid',
      'Manchester United',
      'Liverpool',
      'Chelsea',
      'Arsenal',
    ][index % 6];
    final location =
        ['Bhatbhateni Futsal', 'Dhumbarahi', 'Satdobato'][index % 3];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AcceptChallengeBottomSheet(
        teamName: teamName,
        matchType: matchType,
        location: location,
        timePref: timePref,
        wager: wager,
        streakCount: (index % 4) + 1,
        eloRating: (index * 7) % 100,
      ),
    );
  }
}

class _SimpleCreateChallengeForm extends ConsumerStatefulWidget {
  final ScrollController scrollController;
  const _SimpleCreateChallengeForm({required this.scrollController});

  @override
  ConsumerState<_SimpleCreateChallengeForm> createState() =>
      _SimpleCreateChallengeFormState();
}

class _SimpleCreateChallengeFormState
    extends ConsumerState<_SimpleCreateChallengeForm> {
  // Form controllers
  final TextEditingController _locationCtrl = TextEditingController();
  final TextEditingController _descriptionCtrl = TextEditingController();
  final TextEditingController _rulesCtrl = TextEditingController();
  final TextEditingController _venueCtrl = TextEditingController();

  // Form state
  String _matchType = '5v5';
  AgeGroup _ageGroup = AgeGroup.u18;
  String _skillLevel = 'Intermediate';
  DateTime _proposedDateTime = DateTime.now().add(const Duration(days: 1));
  DateTime? _alternativeDateTime1;
  DateTime? _alternativeDateTime2;
  double _wagerAmount = 0;
  WagerType _wagerType = WagerType.braggingRights;
  int _expirationHours = 168; // 7 days default
  String? _specificOpponentId;
  String? _specificOpponentTeamId;
  String? _challengerTeamId;
  bool _isLoading = false;
  List<String> _selectedVenueIds = []; // New field for multiple venues

  @override
  void dispose() {
    _locationCtrl.dispose();
    _descriptionCtrl.dispose();
    _rulesCtrl.dispose();
    _venueCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: SingleChildScrollView(
        controller: widget.scrollController,
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Create Challenge',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),

            // Match type
            const Text('Match Type', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              runSpacing: 8,
              children: ['5v5', '7v7', '11v11']
                  .map(
                    (t) => ChoiceChip(
                      label: Text(
                        t,
                        style: TextStyle(
                          color:
                              _matchType == t ? Colors.white : Colors.black87,
                          fontWeight: _matchType == t
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                      selected: _matchType == t,
                      onSelected: (_) => setState(() => _matchType = t),
                      selectedColor: const Color(0xffDA22FF),
                      backgroundColor: Colors.white.withOpacity(0.9),
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: _matchType == t
                            ? const Color(0xffDA22FF)
                            : Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                  )
                  .toList(),
            ),

            const SizedBox(height: 16),
            // Age Group
            const Text('Age Group', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              runSpacing: 8,
              children: AgeGroup.values
                  .map(
                    (age) => ChoiceChip(
                      label: Text(
                        age.value,
                        style: TextStyle(
                          color:
                              _ageGroup == age ? Colors.white : Colors.black87,
                          fontWeight: _ageGroup == age
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                      selected: _ageGroup == age,
                      onSelected: (_) => setState(() => _ageGroup = age),
                      selectedColor: const Color(0xffDA22FF),
                      backgroundColor: Colors.white.withOpacity(0.9),
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: _ageGroup == age
                            ? const Color(0xffDA22FF)
                            : Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                  )
                  .toList(),
            ),

            const SizedBox(height: 16),
            // Skill Level
            const Text('Skill Level', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              runSpacing: 8,
              children: ['Beginner', 'Intermediate', 'Advanced', 'Professional']
                  .map(
                    (skill) => ChoiceChip(
                      label: Text(
                        skill,
                        style: TextStyle(
                          color: _skillLevel == skill
                              ? Colors.white
                              : Colors.black87,
                          fontWeight: _skillLevel == skill
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                      selected: _skillLevel == skill,
                      onSelected: (_) => setState(() => _skillLevel = skill),
                      selectedColor: const Color(0xffDA22FF),
                      backgroundColor: Colors.white.withOpacity(0.9),
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: _skillLevel == skill
                            ? const Color(0xffDA22FF)
                            : Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                  )
                  .toList(),
            ),

            const SizedBox(height: 16),
            // Location
            const Text('Location', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            _buildField(
              _locationCtrl,
              hint: 'Enter location (e.g., Dhumbarahi)',
              icon: Icons.place,
            ),

            const SizedBox(height: 16),
            // Venues (optional)
            const Text('Venues (optional)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            SelectedVenuesDisplay(
              selectedVenueIds: _selectedVenueIds,
              onTap: () async {
                final selectedVenues = await showVenueSelectionModal(
                  context: context,
                  selectedVenueIds: _selectedVenueIds,
                  title: 'Select Venues',
                  hint: 'Search and select venues for your match',
                );
                setState(() {
                  _selectedVenueIds = selectedVenues;
                });
              },
            ),

            const SizedBox(height: 16),
            // Proposed Date & Time
            const Text('Proposed Date & Time',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _proposedDateTime,
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(_proposedDateTime),
                  );
                  if (time != null) {
                    setState(() {
                      _proposedDateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                    });
                  }
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.schedule, color: Colors.white70),
                    const SizedBox(width: 12),
                    Text(
                      '${_proposedDateTime.day}/${_proposedDateTime.month}/${_proposedDateTime.year} at ${_proposedDateTime.hour.toString().padLeft(2, '0')}:${_proposedDateTime.minute.toString().padLeft(2, '0')}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),
            // Description (optional)
            const Text('Description (optional)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            _buildField(
              _descriptionCtrl,
              hint: 'Describe your challenge...',
              icon: Icons.description,
            ),

            const SizedBox(height: 16),
            // Rules (optional)
            const Text('Rules (optional)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            _buildField(
              _rulesCtrl,
              hint: 'Any specific rules for this match...',
              icon: Icons.rule,
            ),

            const SizedBox(height: 16),
            // Alternative Date & Time 1 (optional)
            const Text('Alternative Date & Time 1 (optional)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _alternativeDateTime1 ??
                      DateTime.now().add(const Duration(days: 2)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(_alternativeDateTime1 ??
                        DateTime.now().add(const Duration(days: 2))),
                  );
                  if (time != null) {
                    setState(() {
                      _alternativeDateTime1 = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                    });
                  }
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.schedule, color: Colors.white70),
                    const SizedBox(width: 12),
                    Text(
                      _alternativeDateTime1 != null
                          ? '${_alternativeDateTime1!.day}/${_alternativeDateTime1!.month}/${_alternativeDateTime1!.year} at ${_alternativeDateTime1!.hour.toString().padLeft(2, '0')}:${_alternativeDateTime1!.minute.toString().padLeft(2, '0')}'
                          : 'Set alternative time 1',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),
            // Alternative Date & Time 2 (optional)
            const Text('Alternative Date & Time 2 (optional)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _alternativeDateTime2 ??
                      DateTime.now().add(const Duration(days: 3)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (date != null) {
                  final time = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.fromDateTime(_alternativeDateTime2 ??
                        DateTime.now().add(const Duration(days: 3))),
                  );
                  if (time != null) {
                    setState(() {
                      _alternativeDateTime2 = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                    });
                  }
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.schedule, color: Colors.white70),
                    const SizedBox(width: 12),
                    Text(
                      _alternativeDateTime2 != null
                          ? '${_alternativeDateTime2!.day}/${_alternativeDateTime2!.month}/${_alternativeDateTime2!.year} at ${_alternativeDateTime2!.hour.toString().padLeft(2, '0')}:${_alternativeDateTime2!.minute.toString().padLeft(2, '0')}'
                          : 'Set alternative time 2',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),
            // Wager Type
            const Text('Wager Type', style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 10,
              runSpacing: 8,
              children: WagerType.values
                  .map(
                    (type) => ChoiceChip(
                      label: Text(
                        type.value,
                        style: TextStyle(
                          color: _wagerType == type
                              ? Colors.white
                              : Colors.black87,
                          fontWeight: _wagerType == type
                              ? FontWeight.bold
                              : FontWeight.normal,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                      selected: _wagerType == type,
                      onSelected: (_) => setState(() => _wagerType = type),
                      selectedColor: const Color(0xffDA22FF),
                      backgroundColor: Colors.white.withOpacity(0.9),
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: _wagerType == type
                            ? const Color(0xffDA22FF)
                            : Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                  )
                  .toList(),
            ),

            const SizedBox(height: 16),
            // Wager Amount
            const Text('Wager Amount (NPR)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.white70, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Slider(
                    min: 0,
                    max: 2000,
                    divisions: 20,
                    value: _wagerAmount,
                    activeColor: const Color(0xffDA22FF),
                    label: 'NPR ${_wagerAmount.toStringAsFixed(0)}',
                    onChanged: (v) => setState(() => _wagerAmount = v),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    'NPR ${_wagerAmount.toStringAsFixed(0)}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
            // Expiration Hours
            const Text('Challenge Expiration (hours)',
                style: TextStyle(color: Colors.white70)),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.timer, color: Colors.white70, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Slider(
                    min: 24,
                    max: 168,
                    divisions: 6,
                    value: _expirationHours.toDouble(),
                    activeColor: const Color(0xffDA22FF),
                    label: '${_expirationHours}h',
                    onChanged: (v) =>
                        setState(() => _expirationHours = v.toInt()),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '${_expirationHours}h',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(Icons.flash_on, color: Colors.white),
                label: Text(
                  _isLoading ? 'Creating Challenge...' : 'Place Challenge',
                  style: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Gilroy_Bold',
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xffDA22FF),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
                onPressed: _isLoading
                    ? null
                    : () async {
                        // Validate required fields
                        if (_locationCtrl.text.trim().isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Please enter a location'),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Set loading state
                        setState(() {
                          _isLoading = true;
                        });

                        // Get the create challenge use case
                        final createChallengeUseCase =
                            ref.read(createChallengeUseCaseProvider);

                        // Create the challenge parameters
                        final params = CreateChallengeParams(
                          matchType: _matchType,
                          ageGroup: _ageGroup,
                          skillLevel: _skillLevel,
                          location: _locationCtrl.text.trim(),
                          preferredVenue: _venueCtrl.text.trim().isNotEmpty
                              ? _venueCtrl.text.trim()
                              : null,
                          selectedVenueIds: _selectedVenueIds,
                          preferredDateTime: _proposedDateTime,
                          alternativeDateTime1: _alternativeDateTime1,
                          alternativeDateTime2: _alternativeDateTime2,
                          wagerAmount: _wagerAmount,
                          wagerType: _wagerAmount > 0 ? _wagerType : null,
                          description: _descriptionCtrl.text.trim().isNotEmpty
                              ? _descriptionCtrl.text.trim()
                              : null,
                          rules: _rulesCtrl.text.trim().isNotEmpty
                              ? _rulesCtrl.text.trim()
                              : null,
                          specificOpponentId: _specificOpponentId,
                          specificOpponentTeamId: _specificOpponentTeamId,
                          challengerTeamId: _challengerTeamId,
                          tags: [], // TODO: Add tags functionality
                          expirationHours: _expirationHours,
                        );

                        // Create the challenge
                        final result = await createChallengeUseCase(params);

                        result.fold(
                          (error) {
                            // Reset loading state
                            setState(() {
                              _isLoading = false;
                            });

                            // Error - show error message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(error.message),
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          },
                          (challenge) {
                            // Reset loading state
                            setState(() {
                              _isLoading = false;
                            });

                            // Success - close the form and show success message
                            Navigator.of(context).pop();

                            // Build venue info for success message
                            String venueInfo = '';
                            if (_selectedVenueIds.isNotEmpty) {
                              venueInfo =
                                  '\nVenues: ${_selectedVenueIds.length} selected';
                            } else if (_venueCtrl.text.trim().isNotEmpty) {
                              venueInfo = '\nVenue: ${_venueCtrl.text.trim()}';
                            }

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Challenge created successfully!\nMatch: $_matchType\nLocation: ${_locationCtrl.text.trim()}$venueInfo\nDate: ${_proposedDateTime.day}/${_proposedDateTime.month}/${_proposedDateTime.year}',
                                ),
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 3),
                              ),
                            );

                            // Refresh the challenges list
                            ref
                                .read(challengeListStateProvider.notifier)
                                .refresh();
                          },
                        );
                      },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildField(
    TextEditingController ctrl, {
    required String hint,
    required IconData icon,
  }) {
    return TextField(
      controller: ctrl,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: const TextStyle(color: Colors.white54),
        prefixIcon: Icon(icon, color: Colors.white70),
        filled: true,
        fillColor: Colors.white.withOpacity(0.08),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.1)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xffDA22FF)),
        ),
      ),
    );
  }
}

/// Accept Challenge Bottom Sheet Widget
class _AcceptChallengeBottomSheet extends StatefulWidget {
  final String teamName;
  final String matchType;
  final String location;
  final String timePref;
  final String wager;
  final int streakCount;
  final int eloRating;

  const _AcceptChallengeBottomSheet({
    required this.teamName,
    required this.matchType,
    required this.location,
    required this.timePref,
    required this.wager,
    required this.streakCount,
    required this.eloRating,
  });

  @override
  State<_AcceptChallengeBottomSheet> createState() =>
      _AcceptChallengeBottomSheetState();
}

class _AcceptChallengeBottomSheetState
    extends State<_AcceptChallengeBottomSheet> {
  bool _isLoading = false;
  String? _message;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xff1C1C1E),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: DraggableScrollableSheet(
        expand: false,
        initialChildSize: 0.7,
        maxChildSize: 0.85,
        minChildSize: 0.4,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: EdgeInsets.fromLTRB(
            20,
            20,
            20,
            MediaQuery.of(context).viewInsets.bottom + 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: const Color(
                      0xff00E676,
                    ).withOpacity(0.1),
                    child: const Icon(
                      Icons.sports_kabaddi,
                      color: Color(0xff00E676),
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Accept Challenge',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Gilroy_Bold',
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Review details and confirm',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                            fontFamily: 'Gilroy_Medium',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Challenge Details Card
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: lightblue.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white.withOpacity(0.1)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Team header
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.white.withOpacity(0.15),
                          child: const Icon(
                            Icons.group,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.teamName,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Gilroy_Bold',
                                ),
                              ),
                              Text(
                                'Looking for Opponent',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                  fontFamily: 'Gilroy_Medium',
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Badges
                        Column(
                          children: [
                            _buildSmallBadge(
                              Icons.local_fire_department,
                              'Streak x${widget.streakCount}',
                              const Color(0xffFF6B35),
                            ),
                            const SizedBox(height: 4),
                            _buildSmallBadge(
                              Icons.shield,
                              '${widget.eloRating} Elo',
                              const Color(0xffDA22FF),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Challenge Details Grid
                    LayoutBuilder(
                      builder: (context, constraints) {
                        final double itemWidth =
                            (constraints.maxWidth - 12) / 2;
                        final double itemHeight = 60;
                        return GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          childAspectRatio: itemWidth / itemHeight,
                          mainAxisSpacing: 12,
                          crossAxisSpacing: 12,
                          children: [
                            _buildDetailItem(
                              Icons.sports_soccer,
                              'Match Type',
                              widget.matchType,
                            ),
                            _buildDetailItem(
                              Icons.place,
                              'Location',
                              widget.location,
                            ),
                            _buildDetailItem(
                              Icons.schedule,
                              'Time',
                              widget.timePref,
                            ),
                            _buildDetailItem(
                              Icons.attach_money,
                              'Wager',
                              widget.wager,
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Terms and Warnings
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'By accepting this challenge, you agree to the match terms and commit to playing at the specified time and location.',
                        style: TextStyle(
                          color: Colors.orange.withOpacity(0.9),
                          fontSize: 12,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Message Input (Optional)
              const Text(
                'Message (Optional)',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                style: const TextStyle(color: Colors.white),
                maxLines: 3,
                minLines: 1,
                textInputAction: TextInputAction.done,
                onChanged: (value) => _message = value,
                decoration: InputDecoration(
                  hintText: 'Add a message to the challenger (optional)...',
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withOpacity(0.08),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xff00E676)),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(
                          color: Colors.white.withOpacity(0.3),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.white70,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _acceptChallenge,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xff00E676),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'Accept Challenge',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Gilroy_Bold',
                              ),
                            ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSmallBadge(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w600,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.white.withOpacity(0.7), size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 10,
                    fontFamily: 'Gilroy_Medium',
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Gilroy_Medium',
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _acceptChallenge() async {
    setState(() => _isLoading = true);

    // Simulate API call with message
    print('Accepting challenge with message: ${_message ?? "No message"}');
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() => _isLoading = false);
      Navigator.of(context).pop();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Color(0xff00E676)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Challenge Accepted!',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Gilroy_Bold',
                      ),
                    ),
                    Text(
                      'You will be notified with match confirmation details.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xff00E676).withOpacity(0.9),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
}
