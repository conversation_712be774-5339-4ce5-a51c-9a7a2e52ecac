import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../../domain/entities/challenge.dart';
import '../../domain/entities/challenge_enums.dart';
import '../dto/challenge_dto.dart';

abstract interface class ChallengeDataSource {
  // Challenge operations
  Future<PagedChallenges> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  });

  Future<Challenge> getChallengeById(String challengeId);

  Future<Challenge> createChallenge(CreateChallengeRequestDto request);

  Future<Challenge> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request);

  Future<void> deleteChallenge(String challengeId);

  Future<Challenge> respondToChallenge(RespondToChallengeRequestDto request);

  Future<void> submitMatchResult(SubmitMatchResultRequestDto request);

  Future<void> disputeMatchResult(DisputeMatchResultRequestDto request);

  // Statistics and suggestions
  Future<ChallengeStats> getChallengeStats();

  Future<List<MatchSuggestion>> getMatchSuggestions();

  // User's challenges
  Future<PagedChallenges> getMyChallenges({
    int? page,
    int? pageSize,
  });

  // Match requests
  Future<List<MatchSuggestion>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  });

  Future<MatchSuggestion> createMatchRequest(
      CreateMatchRequestRequestDto request);

  Future<List<MatchSuggestion>> getMyMatchRequests({
    int? page,
    int? pageSize,
  });
}

class ChallengeDataSourceImpl implements ChallengeDataSource {
  final ApiClient _apiClient;

  ChallengeDataSourceImpl(this._apiClient);

  @override
  Future<PagedChallenges> getChallenges({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    double? maxWagerAmount,
    bool? hasWager,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? pageSize,
  }) async {
    final queryParams = <String, dynamic>{};

    if (matchType != null) queryParams['MatchType'] = matchType;
    if (location != null) queryParams['Location'] = location;
    if (skillLevel != null) queryParams['SkillLevel'] = skillLevel;
    if (ageGroup != null) queryParams['AgeGroup'] = ageGroup;
    if (maxWagerAmount != null) queryParams['MaxWagerAmount'] = maxWagerAmount;
    if (hasWager != null) queryParams['HasWager'] = hasWager;
    if (fromDate != null) queryParams['FromDate'] = fromDate.toIso8601String();
    if (toDate != null) queryParams['ToDate'] = toDate.toIso8601String();
    if (page != null) queryParams['Page'] = page;
    if (pageSize != null) queryParams['PageSize'] = pageSize;

    final response = await _apiClient.get(
      ApiConst.challengesEndpoint,
      query: queryParams,
    );

    final pagedResponse = PagedChallengesResponseDto.fromJson(response);
    return PagedChallenges(
      items: pagedResponse.items?.map((dto) => _mapDtoToEntity(dto)).toList(),
      total: pagedResponse.total,
      page: pagedResponse.page,
      pageSize: pagedResponse.pageSize,
      totalPages: pagedResponse.totalPages,
    );
  }

  @override
  Future<Challenge> getChallengeById(String challengeId) async {
    final endpoint = ApiConst.challengeDetailEndpoint
        .replaceAll('{challengeId}', challengeId);
    final response = await _apiClient.get(endpoint);

    final detailResponse = ChallengeDetailResponseDto.fromJson(response);
    return _mapDetailDtoToEntity(detailResponse);
  }

  @override
  Future<Challenge> createChallenge(CreateChallengeRequestDto request) async {
    final response = await _apiClient.post(
      ApiConst.challengesEndpoint,
      data: request.toJson(),
    );

    final challengeResponse = ChallengeResponseDto.fromJson(response);
    return _mapDtoToEntity(challengeResponse);
  }

  @override
  Future<Challenge> updateChallenge(
      String challengeId, UpdateChallengeRequestDto request) async {
    final endpoint = ApiConst.challengeDetailEndpoint
        .replaceAll('{challengeId}', challengeId);
    final response = await _apiClient.put(
      endpoint,
      data: request.toJson(),
    );

    final challengeResponse = ChallengeResponseDto.fromJson(response);
    return _mapDtoToEntity(challengeResponse);
  }

  @override
  Future<void> deleteChallenge(String challengeId) async {
    final endpoint = ApiConst.challengeDetailEndpoint
        .replaceAll('{challengeId}', challengeId);
    await _apiClient.delete(endpoint);
  }

  @override
  Future<Challenge> respondToChallenge(
      RespondToChallengeRequestDto request) async {
    final response = await _apiClient.post(
      ApiConst.respondToChallengeEndpoint,
      data: request.toJson(),
    );

    final challengeResponse = ChallengeResponseDto.fromJson(response);
    return _mapDtoToEntity(challengeResponse);
  }

  @override
  Future<void> submitMatchResult(SubmitMatchResultRequestDto request) async {
    await _apiClient.post(
      ApiConst.submitMatchResultEndpoint,
      data: request.toJson(),
    );
  }

  @override
  Future<void> disputeMatchResult(DisputeMatchResultRequestDto request) async {
    await _apiClient.post(
      ApiConst.disputeMatchResultEndpoint,
      data: request.toJson(),
    );
  }

  @override
  Future<ChallengeStats> getChallengeStats() async {
    final response = await _apiClient.get(ApiConst.challengeStatsEndpoint);
    final statsResponse = ChallengeStatsResponseDto.fromJson(response);

    return ChallengeStats(
      totalChallenges: statsResponse.totalChallenges,
      openChallenges: statsResponse.openChallenges,
      acceptedChallenges: statsResponse.acceptedChallenges,
      completedChallenges: statsResponse.completedChallenges,
      wonChallenges: statsResponse.wonChallenges,
      lostChallenges: statsResponse.lostChallenges,
      winRate: statsResponse.winRate,
      disputedChallenges: statsResponse.disputedChallenges,
    );
  }

  @override
  Future<List<MatchSuggestion>> getMatchSuggestions() async {
    final response =
        await _apiClient.get(ApiConst.challengeSuggestionsEndpoint);

    return (response as List<dynamic>)
        .map((json) => MatchSuggestionResponseDto.fromJson(json))
        .map((dto) => _mapSuggestionDtoToEntity(dto))
        .toList();
  }

  @override
  Future<PagedChallenges> getMyChallenges({
    int? page,
    int? pageSize,
  }) async {
    final queryParams = <String, dynamic>{};
    if (page != null) queryParams['page'] = page;
    if (pageSize != null) queryParams['pageSize'] = pageSize;

    final response = await _apiClient.get(
      ApiConst.myChallengesEndpoint,
      query: queryParams,
    );

    final pagedResponse = PagedChallengesResponseDto.fromJson(response);
    return PagedChallenges(
      items: pagedResponse.items?.map((dto) => _mapDtoToEntity(dto)).toList(),
      total: pagedResponse.total,
      page: pagedResponse.page,
      pageSize: pagedResponse.pageSize,
      totalPages: pagedResponse.totalPages,
    );
  }

  @override
  Future<List<MatchSuggestion>> getMatchRequests({
    String? matchType,
    String? location,
    String? skillLevel,
    String? ageGroup,
    int? maxDistance,
    bool? acceptWagers,
    int? page,
    int? pageSize,
  }) async {
    final queryParams = <String, dynamic>{};

    if (matchType != null) queryParams['MatchType'] = matchType;
    if (location != null) queryParams['Location'] = location;
    if (skillLevel != null) queryParams['SkillLevel'] = skillLevel;
    if (ageGroup != null) queryParams['AgeGroup'] = ageGroup;
    if (maxDistance != null) queryParams['MaxDistance'] = maxDistance;
    if (acceptWagers != null) queryParams['AcceptWagers'] = acceptWagers;
    if (page != null) queryParams['Page'] = page;
    if (pageSize != null) queryParams['PageSize'] = pageSize;

    final response = await _apiClient.get(
      ApiConst.matchRequestsEndpoint,
      query: queryParams,
    );

    final pagedResponse = PagedMatchRequestsResponseDto.fromJson(response);
    return pagedResponse.items
            ?.map((dto) => _mapMatchRequestDtoToEntity(dto))
            .toList() ??
        [];
  }

  @override
  Future<MatchSuggestion> createMatchRequest(
      CreateMatchRequestRequestDto request) async {
    final response = await _apiClient.post(
      ApiConst.matchRequestsEndpoint,
      data: request.toJson(),
    );

    final matchRequestResponse = MatchRequestResponseDto.fromJson(response);
    return _mapMatchRequestDtoToEntity(matchRequestResponse);
  }

  @override
  Future<List<MatchSuggestion>> getMyMatchRequests({
    int? page,
    int? pageSize,
  }) async {
    final queryParams = <String, dynamic>{};
    if (page != null) queryParams['page'] = page;
    if (pageSize != null) queryParams['pageSize'] = pageSize;

    final response = await _apiClient.get(
      ApiConst.myMatchRequestsEndpoint,
      query: queryParams,
    );

    final pagedResponse = PagedMatchRequestsResponseDto.fromJson(response);
    return pagedResponse.items
            ?.map((dto) => _mapMatchRequestDtoToEntity(dto))
            .toList() ??
        [];
  }

  // Helper methods to map DTOs to entities
  Challenge _mapDtoToEntity(ChallengeResponseDto dto) {
    return Challenge(
      id: dto.id,
      challengerId: dto.challengerId,
      challengerName: dto.challengerName,
      challengerTeamId: dto.challengerTeamId,
      challengerTeamName: dto.challengerTeamName,
      opponentId: dto.opponentId,
      opponentName: dto.opponentName,
      opponentTeamId: dto.opponentTeamId,
      opponentTeamName: dto.opponentTeamName,
      matchType: dto.matchType,
      ageGroup: AgeGroup.fromString(dto.ageGroup),
      skillLevel: dto.skillLevel,
      location: dto.location,
      venueId: dto.venueId,
      venueName: dto.venueName,
      proposedDateTime: dto.proposedDateTime,
      alternativeDateTime1: dto.alternativeDateTime1,
      alternativeDateTime2: dto.alternativeDateTime2,
      wagerAmount: dto.wagerAmount,
      wagerType: WagerType.fromString(dto.wagerType),
      description: dto.description,
      rules: dto.rules,
      status: dto.status,
      expiresAt: dto.expiresAt,
      acceptedAt: dto.acceptedAt,
      completedAt: dto.completedAt,
      winnerId: dto.winnerId,
      winnerName: dto.winnerName,
      winnerTeamId: dto.winnerTeamId,
      winnerTeamName: dto.winnerTeamName,
      matchResult: dto.matchResult,
      isResultDisputed: dto.isResultDisputed,
      responseCount: dto.responseCount,
      created: dto.created,
    );
  }

  Challenge _mapDetailDtoToEntity(ChallengeDetailResponseDto dto) {
    return Challenge(
      id: dto.id,
      challengerId: dto.challengerId,
      challengerName: dto.challengerName,
      challengerTeamId: dto.challengerTeamId,
      challengerTeamName: dto.challengerTeamName,
      opponentId: dto.opponentId,
      opponentName: dto.opponentName,
      opponentTeamId: dto.opponentTeamId,
      opponentTeamName: dto.opponentTeamName,
      matchType: dto.matchType,
      ageGroup: AgeGroup.fromString(dto.ageGroup),
      skillLevel: dto.skillLevel,
      location: dto.location,
      venueId: dto.venueId,
      venueName: dto.venueName,
      proposedDateTime: dto.proposedDateTime,
      alternativeDateTime1: dto.alternativeDateTime1,
      alternativeDateTime2: dto.alternativeDateTime2,
      wagerAmount: dto.wagerAmount,
      wagerType: WagerType.fromString(dto.wagerType),
      description: dto.description,
      rules: dto.rules,
      status: dto.status,
      expiresAt: dto.expiresAt,
      acceptedAt: dto.acceptedAt,
      completedAt: dto.completedAt,
      winnerId: dto.winnerId,
      winnerName: dto.winnerName,
      winnerTeamId: dto.winnerTeamId,
      winnerTeamName: dto.winnerTeamName,
      matchResult: dto.matchResult,
      isResultDisputed: dto.isResultDisputed,
      responseCount: dto.responseCount,
      created: dto.created,
      responses: dto.responses
          ?.map((r) => ChallengeResponse(
                id: r.id,
                responderId: r.responderId,
                responderName: r.responderName,
                responderTeamId: r.responderTeamId,
                responderTeamName: r.responderTeamName,
                responseType: r.responseType,
                preferredDateTime: r.preferredDateTime,
                message: r.message,
                respondedAt: r.respondedAt,
              ))
          .toList(),
    );
  }

  MatchSuggestion _mapSuggestionDtoToEntity(MatchSuggestionResponseDto dto) {
    return MatchSuggestion(
      id: dto.id,
      type: dto.type,
      matchType: dto.matchType,
      location: dto.location,
      proposedDateTime: dto.proposedDateTime,
      opponentName: dto.opponentName,
      teamName: dto.teamName,
      wagerAmount: dto.wagerAmount,
      compatibilityScore: dto.compatibilityScore,
      description: dto.description,
    );
  }

  MatchSuggestion _mapMatchRequestDtoToEntity(MatchRequestResponseDto dto) {
    return MatchSuggestion(
      id: dto.id,
      type: 'match_request',
      matchType: dto.matchType,
      location: dto.preferredLocation,
      proposedDateTime: dto.preferredDateTime,
      opponentName: dto.requesterName,
      teamName: null,
      wagerAmount: dto.maxWagerAmount,
      compatibilityScore: 0, // Not available in match request
      description: dto.description,
    );
  }
}

// Additional DTOs needed for match requests

class MatchRequestResponseDto {
  final String id;
  final String requesterId;
  final String? requesterName;
  final String? matchType;
  final String? preferredLocation;
  final String? skillLevel;
  final String? ageGroup;
  final DateTime? preferredDateTime;
  final String? description;
  final bool isActive;
  final DateTime expiresAt;
  final int? maxDistance;
  final double? maxWagerAmount;
  final bool acceptWagers;
  final DateTime created;

  const MatchRequestResponseDto({
    required this.id,
    required this.requesterId,
    this.requesterName,
    this.matchType,
    this.preferredLocation,
    this.skillLevel,
    this.ageGroup,
    this.preferredDateTime,
    this.description,
    required this.isActive,
    required this.expiresAt,
    this.maxDistance,
    this.maxWagerAmount,
    required this.acceptWagers,
    required this.created,
  });

  factory MatchRequestResponseDto.fromJson(Map<String, dynamic> json) {
    return MatchRequestResponseDto(
      id: json['id'] as String,
      requesterId: json['requesterId'] as String,
      requesterName: json['requesterName'] as String?,
      matchType: json['matchType'] as String?,
      preferredLocation: json['preferredLocation'] as String?,
      skillLevel: json['skillLevel'] as String?,
      ageGroup: json['ageGroup'] as String?,
      preferredDateTime: json['preferredDateTime'] != null
          ? DateTime.parse(json['preferredDateTime'] as String)
          : null,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      maxDistance: json['maxDistance'] as int?,
      maxWagerAmount: json['maxWagerAmount'] as double?,
      acceptWagers: json['acceptWagers'] as bool,
      created: DateTime.parse(json['created'] as String),
    );
  }
}

class PagedMatchRequestsResponseDto {
  final List<MatchRequestResponseDto>? items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  const PagedMatchRequestsResponseDto({
    this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PagedMatchRequestsResponseDto.fromJson(Map<String, dynamic> json) {
    return PagedMatchRequestsResponseDto(
      items: json['items'] != null
          ? (json['items'] as List<dynamic>)
              .map((e) => MatchRequestResponseDto.fromJson(e))
              .toList()
          : null,
      total: json['total'] as int,
      page: json['page'] as int,
      pageSize: json['pageSize'] as int,
      totalPages: json['totalPages'] as int,
    );
  }
}
