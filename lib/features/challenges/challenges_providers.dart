import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/use_case.dart' hide NoParams;
import 'data/datasources/challenge_datasource.dart';
import 'data/repositories/challenge_repository_impl.dart';
import 'domain/entities/challenge.dart';
import 'domain/repositories/challenge_repository.dart';
import 'domain/usecases/challenge_usecases.dart';
import 'domain/services/streak_bonus_service.dart';
import 'presentation/logic/challenge_state.dart';

import '../../core/networking/api_client.dart';

/// Provider for the API client
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient(ref);
});

/// Provider for the challenge datasource
final challengeDataSourceProvider = Provider<ChallengeDataSource>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return ChallengeDataSourceImpl(apiClient);
});

/// Provider for the challenge repository
final challengeRepositoryProvider = Provider<ChallengeRepository>((ref) {
  final dataSource = ref.watch(challengeDataSourceProvider);
  return ChallengeRepositoryImpl(dataSource);
});

// Use Case Providers
final getChallengesUseCaseProvider = Provider<GetChallengesUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetChallengesUseCase(repository);
});

final getChallengeByIdUseCaseProvider =
    Provider<GetChallengeByIdUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetChallengeByIdUseCase(repository);
});

final createChallengeUseCaseProvider = Provider<CreateChallengeUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return CreateChallengeUseCase(repository);
});

final updateChallengeUseCaseProvider = Provider<UpdateChallengeUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return UpdateChallengeUseCase(repository);
});

final deleteChallengeUseCaseProvider = Provider<DeleteChallengeUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return DeleteChallengeUseCase(repository);
});

final respondToChallengeUseCaseProvider =
    Provider<RespondToChallengeUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return RespondToChallengeUseCase(repository);
});

final submitMatchResultUseCaseProvider =
    Provider<SubmitMatchResultUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return SubmitMatchResultUseCase(repository);
});

final disputeMatchResultUseCaseProvider =
    Provider<DisputeMatchResultUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return DisputeMatchResultUseCase(repository);
});

final getChallengeStatsUseCaseProvider =
    Provider<GetChallengeStatsUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetChallengeStatsUseCase(repository);
});

final getMatchSuggestionsUseCaseProvider =
    Provider<GetMatchSuggestionsUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetMatchSuggestionsUseCase(repository);
});

final getMyChallengesUseCaseProvider = Provider<GetMyChallengesUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetMyChallengesUseCase(repository);
});

final getMatchRequestsUseCaseProvider =
    Provider<GetMatchRequestsUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetMatchRequestsUseCase(repository);
});

final createMatchRequestUseCaseProvider =
    Provider<CreateMatchRequestUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return CreateMatchRequestUseCase(repository);
});

final getMyMatchRequestsUseCaseProvider =
    Provider<GetMyMatchRequestsUseCase>((ref) {
  final repository = ref.watch(challengeRepositoryProvider);
  return GetMyMatchRequestsUseCase(repository);
});

// Service Providers
final streakBonusServiceProvider = Provider<StreakBonusService>((ref) {
  return StreakBonusService();
});

// State Management Providers

// Challenge List State
final challengeListStateProvider =
    StateNotifierProvider<ChallengeListNotifier, ChallengeListState>((ref) {
  final getChallengesUseCase = ref.watch(getChallengesUseCaseProvider);

  return ChallengeListNotifier(
    getChallengesUseCase: getChallengesUseCase,
  );
});

// Challenge Details State
final challengeDetailsStateProvider = StateNotifierProvider.family<
    ChallengeDetailsNotifier,
    ChallengeDetailsState,
    String>((ref, challengeId) {
  final repository = ref.watch(challengeRepositoryProvider);
  final respondToChallengeUseCase =
      ref.watch(respondToChallengeUseCaseProvider);
  final submitMatchResultUseCase = ref.watch(submitMatchResultUseCaseProvider);

  return ChallengeDetailsNotifier(
    challengeId: challengeId,
    repository: repository,
    respondToChallengeUseCase: respondToChallengeUseCase,
    submitMatchResultUseCase: submitMatchResultUseCase,
  );
});

// Create Challenge State
final createChallengeStateProvider =
    StateNotifierProvider<CreateChallengeNotifier, CreateChallengeState>((ref) {
  final createChallengeUseCase = ref.watch(createChallengeUseCaseProvider);

  return CreateChallengeNotifier(createChallengeUseCase);
});

// Filter State for Challenge List
final challengeFiltersProvider =
    StateNotifierProvider<ChallengeFiltersNotifier, ChallengeFilters>((ref) {
  return ChallengeFiltersNotifier();
});

// Quick Access Providers for UI

// Current user challenges
final currentUserChallengesProvider =
    FutureProvider.autoDispose<PagedChallenges>((ref) async {
  // TODO: Get current user ID from auth
  const currentUserId = 'current_user_id';

  final getMyChallengesUseCase = ref.watch(getMyChallengesUseCaseProvider);
  final result = await getMyChallengesUseCase(
    GetMyChallengesParams(
      page: 1,
      pageSize: 20,
    ),
  );

  return result.fold(
    (error) => throw error,
    (challenges) => challenges,
  );
});

// Challenge stats
final challengeStatsProvider =
    FutureProvider.autoDispose<ChallengeStats>((ref) async {
  final getChallengeStatsUseCase = ref.watch(getChallengeStatsUseCaseProvider);
  final result = await getChallengeStatsUseCase(const NoParams());

  return result.fold((error) => throw error, (stats) => stats);
});

// Match suggestions
final matchSuggestionsProvider =
    FutureProvider.autoDispose<List<MatchSuggestion>>((ref) async {
  final getMatchSuggestionsUseCase =
      ref.watch(getMatchSuggestionsUseCaseProvider);
  final result = await getMatchSuggestionsUseCase(const NoParams());

  return result.fold((error) => throw error, (suggestions) => suggestions);
});

// Match requests
final matchRequestsProvider =
    FutureProvider.autoDispose<List<MatchSuggestion>>((ref) async {
  final getMatchRequestsUseCase = ref.watch(getMatchRequestsUseCaseProvider);
  final result = await getMatchRequestsUseCase(
    GetMatchRequestsParams(
      page: 1,
      pageSize: 20,
    ),
  );

  return result.fold((error) => throw error, (requests) => requests);
});
