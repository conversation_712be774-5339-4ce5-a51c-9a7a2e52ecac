import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../logic/onboarding_controller.dart';
import '../../../../utils/color.dart';
import '../../domain/entities/onboarding_data.dart';

class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(onboardingNotifierProvider.notifier).checkOnboardingStatus();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final controller = ref.read(onboardingNotifierProvider.notifier);

    return Scaffold(
      backgroundColor: PrimeryColor,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: () {
                    controller.completeOnboarding();
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  child: Text(
                    'Skip',
                    style: TextStyle(
                      color: grey,
                      fontSize: 16,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ),
              ),
            ),

            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  controller.goToPage(index);
                },
                itemCount: onboardingState.pages.length,
                itemBuilder: (context, index) {
                  final page = onboardingState.pages[index];
                  return _buildOnboardingPage(page);
                },
              ),
            ),

            // Bottom navigation
            _buildBottomNavigation(controller, onboardingState),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingData page) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image placeholder (you can replace with actual images)
          Container(
            height: 300,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xffDA22FF).withOpacity(0.3),
                  const Color(0xff9733EE).withOpacity(0.3),
                ],
              ),
            ),
            child: Icon(
              Icons.sports_soccer,
              size: 120,
              color: Colors.white.withOpacity(0.8),
            ),
          ),

          const SizedBox(height: 40),

          // Title
          Text(
            page.title,
            style: const TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            page.description,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 16,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(
    OnboardingNotifier controller,
    OnboardingState state,
  ) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Page indicators
          Row(
            children: List.generate(
              state.pages.length,
              (index) => Container(
                margin: const EdgeInsets.only(right: 8),
                width: index == state.currentPage ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color:
                      index == state.currentPage
                          ? const Color(0xffDA22FF)
                          : grey.withOpacity(0.3),
                ),
              ),
            ),
          ),

          // Navigation buttons
          Row(
            children: [
              // Previous button
              if (!controller.isFirstPage)
                TextButton(
                  onPressed: () {
                    controller.previousPage();
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                  child: Text(
                    'Previous',
                    style: TextStyle(
                      color: grey,
                      fontSize: 16,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ),

              const SizedBox(width: 16),

              // Next/Get Started button
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: const LinearGradient(
                    colors: [Color(0xffDA22FF), Color(0xff9733EE)],
                  ),
                ),
                child: ElevatedButton(
                  onPressed: () {
                    if (controller.isLastPage) {
                      controller.completeOnboarding();
                      Navigator.pushReplacementNamed(context, '/login');
                    } else {
                      controller.nextPage();
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    controller.isLastPage ? 'Get Started' : 'Next',
                    style: const TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
