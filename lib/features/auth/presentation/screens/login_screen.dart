import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../logic/controller.dart';
import '../logic/auth_state.dart';
import '../../../../utils/color.dart';
import '../../../../utils/responsive_utils.dart';
import '../../../../core/widgets/blaze_text_form_field.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../core/config/theme.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String _selectedRole = 'PLAYER';

  // Role selection chips
  Widget _buildRoleChip({
    required String label,
    required bool selected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            color: selected ? const Color(0xffDA22FF) : lightblue,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: selected ? const Color(0xffDA22FF) : grey.withOpacity(0.2),
            ),
          ),
          alignment: Alignment.center,
          child: Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: selected ? Colors.white : grey,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      // Clear any previous error to avoid showing stale messages
      ref.read(authNotifierProvider.notifier).clearError();

      final resp = await ref.read(authNotifierProvider.notifier).loginWithPhone(
            phoneNumber: _phoneController.text.trim(),
            password: _passwordController.text.trim(),
            role: _selectedRole,
          );

      if (!resp && mounted) {
        final latestState = ref.read(authNotifierProvider);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(latestState.errorMessage ?? 'Login failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToForgotPassword() {
    context.go('/forgot-password');
  }

  void _navigateToSignUp() {
    context.go('/register');
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final isDark = ref.watch(theme_providers.isDarkModeProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      if (mounted && next.status == AuthStatus.authenticated) {
        // Add a small delay to ensure state is properly updated
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            context.go('/home');
          }
        });
      }
    });

    return Scaffold(
      backgroundColor: NextSportzTheme.getPrimaryColor(isDark),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(
            top: 12,
            bottom: 40,
          ), // Add safe paddings for notches/status bar
          child: Column(
            children: [
              // Header with background image and logo
              Stack(
                children: [
                  Center(
                    child: Image.asset(
                      "assets/images/backphoto.png",
                      height: MediaQuery.of(context).size.height *
                          0.25, // Reduced from /3 to 25%
                    ),
                  ),
                  Column(
                    children: [
                      SizedBox(height: MediaQuery.of(context).size.height / 20),
                      Stack(
                        children: [
                          Center(
                            child: Image.asset(
                              "assets/images/award.png",
                              height: MediaQuery.of(context).size.height *
                                  0.18, // Reduced from /4 to 18%
                            ),
                          ),
                          Column(
                            children: [
                              SizedBox(
                                height: MediaQuery.of(context).size.height / 15,
                              ),
                              Center(
                                child: Image.asset(
                                  "assets/images/circulimage.png",
                                  height:
                                      MediaQuery.of(context).size.height / 25,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: MediaQuery.of(context).size.height / 30),

              // Login form
              Form(
                key: _formKey,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                child: Column(
                  children: [
                    // Phone number field
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: BlazeTextFormField(
                        hintText: "Phone Number",
                        prefixIcon: Icons.phone_outlined,
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your phone number';
                          }
                          if (value.length < 10) {
                            return 'Please enter a valid phone number';
                          }
                          return null;
                        },
                      ),
                    ),

                    SizedBox(height: MediaQuery.of(context).size.height / 40),

                    // Password field
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: BlazeTextFormField(
                        hintText: "Password",
                        prefixIcon: Icons.lock_outline,
                        controller: _passwordController,
                        isPassword: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your password';
                          }
                          if (value.length < 6) {
                            return 'Password must be at least 6 characters';
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => _login(),
                      ),
                    ),

                    SizedBox(height: MediaQuery.of(context).size.height / 40),

                    // Login button
                    Center(
                      child: GestureDetector(
                        onTap: authState.status == AuthStatus.loading
                            ? null
                            : _login,
                        child: Container(
                          height: MediaQuery.of(context).size.height / 17,
                          width: kIsWeb
                              ? 400
                              : MediaQuery.of(context).size.width / 1.2,
                          margin: EdgeInsets.symmetric(horizontal: 20),
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(20)),
                            gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Color(0xffDA22FF),
                                Color(0xff9733EE),
                                Color(0xffDA22FF),
                              ],
                            ),
                          ),
                          child: Center(
                            child: authState.status == AuthStatus.loading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    authState.status == AuthStatus.authenticated
                                        ? "Success!"
                                        : "Log In",
                                    style: const TextStyle(
                                      fontFamily: 'Gilroy Medium',
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 15,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ),
                    // Forgot password link
                    SizedBox(height: ResponsiveUtils.responsiveSmSpacing),
                    Row(
                      children: [
                        const Spacer(),
                        GestureDetector(
                          onTap: _navigateToForgotPassword,
                          child: Container(
                            height: MediaQuery.of(context).size.height / 30,
                            decoration: const BoxDecoration(
                              color: Colors.transparent,
                              border: Border(
                                bottom: BorderSide(
                                  color: Color(0xffe2e3e9),
                                  width: 1,
                                ),
                              ),
                            ),
                            child: const Center(
                              child: Text(
                                "Forgot password?",
                                style: TextStyle(
                                  fontFamily: 'Gilroy Medium',
                                  color: Color(0xffe2e3e9),
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: ResponsiveUtils.responsiveXlSpacing),
                      ],
                    ),

                    SizedBox(height: ResponsiveUtils.responsiveXlSpacing),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 30),
                      padding: const EdgeInsets.symmetric(
                        vertical: 20,
                        horizontal: 20,
                      ),
                      decoration: BoxDecoration(
                        color: lightblue.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          const Text(
                            "Don't have an account?",
                            style: TextStyle(
                              fontFamily: 'Gilroy_Medium',
                              color: Color(0xff767d97),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 15),
                          GestureDetector(
                            onTap: _navigateToSignUp,
                            child: Container(
                              width: double.infinity,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: const Color(0xffDA22FF),
                                  width: 2,
                                ),
                              ),
                              child: const Center(
                                child: Text(
                                  "Sign Up Here",
                                  style: TextStyle(
                                    fontFamily: 'Gilroy_Medium',
                                    color: Color(0xffDA22FF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Error message (tappable to clear error)
                    if (authState.status == AuthStatus.error)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: GestureDetector(
                          onTap: () => ref
                              .read(authNotifierProvider.notifier)
                              .clearError(),
                          child: Text(
                            authState.errorMessage ?? 'An error occurred',
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    // Ensure bottom spacing for notch devices
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
