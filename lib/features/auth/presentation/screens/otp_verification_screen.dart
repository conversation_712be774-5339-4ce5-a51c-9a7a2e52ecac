import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/widgets/blaze_text_form_field.dart';
import '../logic/controller.dart';
import '../logic/auth_state.dart';
import '../../../../utils/color.dart';

class OtpVerificationScreen extends ConsumerStatefulWidget {
  final String phoneNumber;
  final String role;

  const OtpVerificationScreen({
    super.key,
    required this.phoneNumber,
    required this.role,
  });

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  bool _isLoading = false;

  Future<void> _verifyOtp() async {
    if (!_isLoading && _formKey.currentState!.validate()) {
      setState(() => _isLoading = true);

      final authNotifier = ref.read(authNotifierProvider.notifier);
      // Clear any previous error before a fresh attempt
      authNotifier.clearError();

      final success = await authNotifier.verifyOtp(
        phoneNumber: widget.phoneNumber,
        otp: _otpController.text,
      );

      if (!mounted) return;
      setState(() => _isLoading = false);

      if (success) {
        // Show success message and navigate to login
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Phone number verified successfully! Please login.'),
            backgroundColor: Colors.green,
          ),
        );
        context.go('/login');
      }
    }
  }

  Future<void> _resendOtp() async {
    // Since there's no resend OTP endpoint, we'll show a message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Please contact support to resend OTP'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);

    // Handle auth state changes
    if (authState.status == AuthStatus.error && _isLoading) {
      setState(() => _isLoading = false);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authState.errorMessage ?? 'OTP verification failed'),
            backgroundColor: Colors.red,
          ),
        );
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify OTP'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                const Icon(
                  Icons.phone_android,
                  size: 80,
                  color: Color(0xffDA22FF),
                ),
                const SizedBox(height: 24),
                Text(
                  'Verify Your Phone Number',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: grey,
                    fontFamily: 'Gilroy_Bold',
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'We\'ve sent a verification code to\n${widget.phoneNumber}',
                  style: TextStyle(
                    fontSize: 16,
                    color: grey.withOpacity(0.7),
                    fontFamily: 'Gilroy_Medium',
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // OTP Input
                BlazeTextFormField(
                  hintText: 'Enter OTP',
                  prefixIcon: Icons.lock_outline,
                  controller: _otpController,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  validator: (v) {
                    if (v == null || v.isEmpty) return 'Enter OTP';
                    if (v.length < 4) return 'OTP must be at least 4 digits';
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Verify Button
                ElevatedButton(
                  onPressed: _isLoading ? null : _verifyOtp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xffDA22FF),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Verify OTP',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Gilroy_Bold',
                          ),
                        ),
                ),
                const SizedBox(height: 16),

                // Resend OTP
                TextButton(
                  onPressed: _isLoading ? null : _resendOtp,
                  child: Text(
                    'Resend OTP',
                    style: TextStyle(
                      color: const Color(0xffDA22FF),
                      fontSize: 16,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Back to Login
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Back to Login',
                    style: TextStyle(
                      color: grey.withOpacity(0.7),
                      fontSize: 14,
                      fontFamily: 'Gilroy_Medium',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
