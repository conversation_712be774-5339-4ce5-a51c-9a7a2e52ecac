import 'package:fpdart/fpdart.dart';
import '../entities/user.dart';
import '../../../../core/networking/app_error.dart';

abstract class AuthRepository {
  Future<Either<AppError, User>> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  });

  Future<Either<AppError, User>> register({
    required String name,
    required String email,
    required String phoneNumber,
    required String password,
    required String role,
  });

  Future<Either<AppError, User>> verifyOtp({
    required String phoneNumber,
    required String otp,
  });

  Future<Either<AppError, void>> forgotPassword({required String email});

  Future<Either<AppError, void>> resetPassword({
    required String email,
    required String newPassword,
    required String confirmPassword,
  });

  Future<Either<AppError, void>> logout();

  Future<Either<AppError, User?>> getCurrentUser();

  Future<Either<AppError, void>> updateProfile({
    required String name,
    required String email,
    String? profileImage,
  });

  Future<Either<AppError, bool>> isAuthenticated();
}
